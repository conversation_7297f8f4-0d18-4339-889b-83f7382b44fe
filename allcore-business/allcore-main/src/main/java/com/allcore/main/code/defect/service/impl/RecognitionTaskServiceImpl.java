package com.allcore.main.code.defect.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.enums.BizEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.common.utils.FileUtils;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.api.ResultCode;
import com.allcore.core.tool.jackson.JsonUtil;
import com.allcore.core.tool.utils.*;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.feign.IAlarmClient;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.defect.dto.PicTagNoComponentDTO;
import com.allcore.main.code.defect.dto.RecognitionTaskDTO;
import com.allcore.main.code.defect.entity.RecognitionTask;
import com.allcore.main.code.defect.entity.RecognitionTaskAlgorithm;
import com.allcore.main.code.defect.mapper.RecognitionTaskMapper;
import com.allcore.main.code.defect.props.DefectProperties;
import com.allcore.main.code.defect.service.IRecognitionTaskAlgorithmService;
import com.allcore.main.code.defect.service.IRecognitionTaskService;
import com.allcore.main.code.defect.vo.*;
import com.allcore.main.code.inspection.dto.InspectionPictureTaggingDTO;
import com.allcore.main.code.inspection.entity.InspectionDeviceDetail;
import com.allcore.main.code.inspection.entity.InspectionPicture;
import com.allcore.main.code.inspection.entity.InspectionPictureTagging;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.mapper.InspectionPictureMapper;
import com.allcore.main.code.inspection.mapper.InspectionPictureTaggingMapper;
import com.allcore.main.code.inspection.mapper.InspectionTaskMapper;
import com.allcore.main.code.inspection.service.IInspectionDeviceDetailService;
import com.allcore.main.code.inspection.service.IInspectionPictureService;
import com.allcore.main.code.inspection.service.IInspectionPictureTaggingService;
import com.allcore.main.code.inspection.service.IInspectionTaskService;
import com.allcore.main.code.inspection.vo.InspectionPictureAlgorithmVO;
import com.allcore.main.code.inspection.vo.InspectionPictureGroupVO;
import com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO;
import com.allcore.main.code.inspection.vo.InspectionPictureVO;
import com.allcore.main.code.solve.dto.RemoveTaskDTO;
import com.allcore.main.code.solve.service.IRemoveTaskService;
import com.allcore.main.code.source.entity.PvComponents;
import com.allcore.main.code.source.mapper.LinePicMapper;
import com.allcore.main.code.source.mapper.PvAreaPicMapper;
import com.allcore.main.code.source.mapper.PvComponentsMapper;
import com.allcore.main.code.source.props.SourceProperties;
import com.allcore.main.code.source.service.IPvComponentsService;
import com.allcore.main.code.source.vo.DeviceFileVO;
import com.allcore.main.code.source.vo.DeviceWithParentVO;
import com.allcore.main.code.station.entity.StationManagement;
import com.allcore.main.code.station.mapper.StationManagementMapper;
import com.allcore.main.code.system.entity.AlgorithmManufacturers;
import com.allcore.main.code.system.entity.Strategy;
import com.allcore.main.code.system.mapper.StrategyDetailMapper;
import com.allcore.main.code.system.mapper.StrategyMapper;
import com.allcore.main.code.system.props.SystemProperties;
import com.allcore.main.code.system.service.IAlgorithmManufacturersService;
import com.allcore.main.code.system.vo.StrategyDetailVO;
import com.allcore.main.utils.*;
import com.allcore.system.feign.ISysClient;
import com.allcore.system.vo.DeptVO;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.*;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import com.deepoove.poi.policy.ListRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.allcore.core.algorithm.invoke.TaskManager;
import org.allcore.core.algorithm.invoke.param.*;
import org.allcore.core.algorithm.service.IAlgorithmCallbackService;
import org.allcore.core.algorithm.service.IAlgorithmToolComponentService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import javax.imageio.stream.MemoryCacheImageInputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.geom.Line2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.allcore.common.constant.BasicConstant.*;
import static com.allcore.main.constant.MainConstant.*;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@AllArgsConstructor
@Slf4j
public class RecognitionTaskServiceImpl extends ZxhcServiceImpl<RecognitionTaskMapper, RecognitionTask>
        implements IRecognitionTaskService, IAlgorithmCallbackService {

    private final IRemoveTaskService removeTaskService;
    private final IInspectionPictureService inspectionPictureService;
    private final IInspectionPictureTaggingService inspectionPictureTaggingService;
    private final InspectionPictureMapper inspectionPictureMapper;
    private final InspectionPictureTaggingMapper inspectionPictureTaggingMapper;
    private final InspectionTaskMapper inspectionTaskMapper;
    private final IInspectionDeviceDetailService inspectionDeviceDetailService;
    private final IPvComponentsService pvComponentsService;
    private final PvComponentsMapper pvComponentsMapper;
    private final StrategyDetailMapper strategyDetailMapper;
    private final StrategyMapper strategyMapper;
    private final LinePicMapper linePicMapper;
    private final PvAreaPicMapper pvAreaPicMapper;

    private final IOssClient ossClient;
    private final ISysClient sysClient;

    private final DefectProperties defectProperties;

    private final SourceProperties sourceProperties;

    private final ApplicationContext applicationContext;

    private final StationManagementMapper stationManagementMapper;

    private final SystemProperties systemProperties;

    private final TaskManager taskManager;

    private final IAlgorithmToolComponentService algorithmToolComponentService;

    private final IAlgorithmManufacturersService algorithmManufacturersService;

    private final IRecognitionTaskAlgorithmService recognitionTaskAlgorithmService;
    private final IAlarmClient alarmClient;
    private final AllcoreRedis redis;

    /**
     * 本次缺陷任务的对应平台缺陷code前缀101、102、103、104
     */
    private static final String DEFECT_RECOGNITION_TASK_CODE = "defect:recognitionTask:code:";

    @Override
    public IPage<RecognitionTaskVO> selectRecognitionTaskPage(IPage<RecognitionTaskVO> page, RecognitionTaskDTO dto) {
        // 插入单位code,无值查自己所有，如果有值则查询值下的
        if (StringUtil.isBlank(dto.getDeptCode())) {
            dto.setDeptCode(AuthUtil.getDeptCode());
        }
        List<RecognitionTaskVO> list = baseMapper.selectRecognitionTaskPage(page, dto);

        if (CollectionUtil.isNotEmpty(list)) {
            List<String> ids = list.stream().map(RecognitionTaskVO::getId).collect(Collectors.toList());

            List<RecognitionTaskVO> group = inspectionPictureTaggingMapper.groupByTask(ids);
            List<RecognitionTaskVO> groupNormal = inspectionPictureTaggingMapper.groupByTaskNormal(ids);
            list.forEach(e -> {
                group.forEach(groupOne -> {
                    if (e.getId().equals(groupOne.getId()) && e.getDeviceType().equals(PV)) {
                        e.setDefectTagNum(groupOne.getDefectTagNum());
                    }
                });
                groupNormal.forEach(groupOne -> {
                    if (e.getId().equals(groupOne.getId()) && !e.getDeviceType().equals(PV)) {
                        e.setDefectTagNum(groupOne.getDefectTagNum());
                    }
                });
            });
        }

        return page.setRecords(list);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R algorithmIdentify(String id, String inspectionTaskId, String strategyId, String algorithmType) {

        log.info(
                "------------------------------------------------------------------开始执行算法----------------------------------------------------");
        RecognitionTask taskInfo = new RecognitionTask();
        List<InspectionPicture> pictures = new ArrayList<>();
        List<StrategyDetailVO> strategyDetailList = new ArrayList<>();

        // 调用算法之前的校验
        String shapesPre =
                algorithmIdentifyCheck(id, inspectionTaskId, taskInfo, pictures, strategyDetailList, strategyId);

        //根据单位查询对应的光伏板平均高度
        String aveStationHeight = stationManagementMapper.getAveStationHeight(shapesPre);
        String pvpHeight = StringUtil.isNotBlank(aveStationHeight) ? aveStationHeight : StringPool.ONE;

        // 调用算法的数据预处理
        List<InspectionPictureAlgorithmVO> algorithmVOList = new ArrayList<>();
        algorithmIdentifyPre(pictures, algorithmVOList);

        List<InspectionPictureAlgorithmVO> fanPicList = algorithmVOList.stream()
                .filter(p -> p.getDeviceType() != null && FAN.equals(p.getDeviceType())).collect(Collectors.toList());
        List<InspectionPictureAlgorithmVO> linePicList = algorithmVOList.stream()
                .filter(p -> p.getDeviceType() != null && TMS_LINE.equals(p.getDeviceType())).collect(Collectors.toList());
        List<InspectionPictureAlgorithmVO> pvPicList = algorithmVOList.stream()
                .filter(p -> p.getDeviceType() != null && PV.equals(p.getDeviceType())).collect(Collectors.toList());
        /**
         * 生成调用算法参数 img_path_list 图片路径列表 图片完整路径+ ’-+-” + 缺陷图片明细guid 不需要鉴权，路径之间使用逗号隔开 post_url 回调地址 用于接收算法识别结果返回
         * model_list 检测模型列表 如果不带这个参数默认全部检测，模型名之间使用逗号隔开 batch_id 批次号 algorithm_type 算法类型风光线
         */
//        Map<String, String> map;
        int okSize = 0;
        DefectProperties.AlgorithmConfig algorithm = defectProperties.getAlgorithm();
        String deviceType = taskInfo.getDeviceType();
        String defectCodePrefix = "nil";
        if (StrUtil.containsIgnoreCase(deviceType, "LINE")) {
//            List<String> towerGuidList = pictureDetails.stream().map(PictureDetail::getDeviceGuid).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//            Map<String, String> majorMap = accountInfoClient.getMajorByTowerGuidList(Collections.singletonList(towerGuidList.get(0)));
//            if (majorMap == null) {
//                return R.fail("未获取到专业信息");
//            }
//            String[] algorithmTypeArr = algorithmType.split(StringPool.COMMA);
//            List<String> majors = majorMap.values().stream().distinct().collect(Collectors.toList());
//            if (CollUtil.isEmpty(majors)) {
//                algorithmType = algorithmTypeArr[0];
//            } else {
//                String major = majors.get(0);
//                for (String item : algorithmTypeArr) {
//                    if (item.equals(major)) {
//                        algorithmType = major;
//                        break;
//                    }
//                }
//            }
//            if (StrUtil.equalsIgnoreCase(algorithmType, BizDictEnum.MAJOR_TMS.getCode())) {
//                defectCodePrefix = "104";
//            } else {
//                defectCodePrefix = "101";
//            }
            //  默认配电
            defectCodePrefix = "101";
        } else if (StrUtil.containsIgnoreCase(deviceType, BizDictEnum.DEVICE_TYPE_FAN.getCode())) {
            defectCodePrefix = "102";
        } else if (StrUtil.containsIgnoreCase(deviceType, BizDictEnum.DEVICE_TYPE_PV.getCode())) {
            defectCodePrefix = "103";
        }

        // 组装数据调用算法
        for (StrategyDetailVO strategyDetailMap : strategyDetailList) {
            String strategyDetailModel = strategyDetailMap.getStrategyDetailModel();
            if (StrUtil.isBlank(strategyDetailModel)) {
                throw new ServiceException("算法模型不能为空");
            }
            String taskId = IdUtil.fastSimpleUUID();
            AlgorithmRequestFile commonRequestInfo = new AlgorithmRequestFile();
            commonRequestInfo.setTaskId(taskId);
            commonRequestInfo.setAlgorithmType(strategyDetailModel);
            commonRequestInfo.setAlgorithmUrl(strategyDetailMap.getAlgorithmAddress());
            commonRequestInfo.setCallbackUrl(algorithm.getCallbackUrl());
            List<AlgorithmRequestFile> requestFiles = getAlgorithmRequestFileList(commonRequestInfo, algorithmVOList, strategyDetailMap.getDeviceType());
            if (ObjectUtil.isNotEmpty(requestFiles)) {
                AlgorithmRequestTask task = AlgorithmRequestTask.builder()
                        .taskId(taskId)
                        .recognitionTaskId(taskInfo.getId())
                        .fileList(requestFiles)
                        .callTimeout(algorithm.getCallTimeout())
                        .invokePoolSize(algorithm.getInvokePoolSize())
                        .keepAliveSeconds(algorithm.getKeepAliveSeconds())
                        .isNeedInterrupt(algorithm.getIsNeedInterrupt())
                        .build();
                taskManager.addTask(task);
                redis.setEx(DEFECT_RECOGNITION_TASK_CODE.concat(taskInfo.getId()), defectCodePrefix, Duration.ofDays(2));
            }

//            map = Maps.newHashMap();
//            map.put("batch_id", id);
//            map.put("pvp_height", pvpHeight);
//            map.put("img_path_list",
//                buildImgPathList(strategyDetailMap, fanPicList, linePicList, pvPicList, map, shapesPre));
//            if (StringUtil.isBlank(map.get("img_path_list"))) {
//                continue;
//            }
//            log.info("开始调用算法");
//            boolean result = assembleAlgorithm(strategyDetailMap, map);
//            if (!result) {
//                return R.fail("算法调用请求发送失败:" + strategyDetailMap.getDeviceType());
//            }
            okSize++;
        }
        if (okSize == 0) {
            return R.fail("没有能成功执行的算法");
        }
        // 修改任务状态
        update(new LambdaUpdateWrapper<RecognitionTask>()
                .set(RecognitionTask::getRecognitionTaskStatus, BizDictEnum.RECOGNITION_STATUS_TO_IDENTIFY.getCode())
                .eq(RecognitionTask::getId, id));
        return R.success("算法识别正在进行中,请稍后刷新列表查询");
    }

    private List<AlgorithmRequestFile> getAlgorithmRequestFileList(AlgorithmRequestFile commonRequestInfo, List<InspectionPictureAlgorithmVO> algorithmVOList, String deviceType) {
        List<AlgorithmRequestFile> requestFiles = new ArrayList<>();
        List<String> fileGuidList = algorithmVOList.stream().filter(i -> {
            if (StringUtil.isBlank(i.getGroupId())) {
                i.setGroupId(CommonUtil.generateUuid());
            }
            return StringUtil.equals(i.getDeviceType(), deviceType);
        }).map(InspectionPictureAlgorithmVO::getFileGuid).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(fileGuidList)) {
            return requestFiles;
        }
        R<List<AllcoreFileVO>> filesDetailR = ossClient.getFilesDetail(fileGuidList);
        if (!filesDetailR.isSuccess()) {
            return requestFiles;
        }
        Map<String, AllcoreFileVO> attachMap = filesDetailR.getData().stream().collect(Collectors.toMap(AllcoreFileVO::getFileGuid, Function.identity()));
        if (StringUtil.equals(deviceType, BizDictEnum.DEVICE_TYPE_PV.getCode())) {
            Map<String, List<InspectionPictureAlgorithmVO>> entry = algorithmVOList.stream().collect(Collectors.groupingBy(InspectionPictureAlgorithmVO::getGroupId));
            for (Map.Entry<String, List<InspectionPictureAlgorithmVO>> picEntry : entry.entrySet()) {
                AlgorithmRequestFile requestFile = BeanUtil.copyProperties(commonRequestInfo, AlgorithmRequestFile.class);
                requestFile.setLocation(sourceProperties.getShapesSystem() + StringPool.UNDERSCORE + picEntry.getValue().get(0).getDeptCode() + "_shapes");
                List<AlgorithmRequestFileDetail> fileDetailList = new ArrayList<>(Collections.nCopies(2, null));
                for (InspectionPictureAlgorithmVO pictureDetail : picEntry.getValue()) {
                    AllcoreFileVO attach = attachMap.get(pictureDetail.getFileGuid());
                    String picName = pictureDetail.getOriginalName().split("\\.")[0];
                    // _T 红外
                    if (StrUtil.endWith(picName, "_T")) {
                        fileDetailList.set(1, AlgorithmRequestFileDetail.builder()
                                .fileId(pictureDetail.getFileGuid())
                                .filePath(attach != null ? attach.getStaticPathNet() : "")
                                .build());
                    } else {
                        fileDetailList.set(0, AlgorithmRequestFileDetail.builder()
                                .fileId(pictureDetail.getFileGuid())
                                .filePath(attach != null ? attach.getStaticPathNet() : "")
                                .build());
                    }
                }
                requestFile.setFileList(fileDetailList);
                requestFiles.add(requestFile);
            }
        } else {
            for (InspectionPictureAlgorithmVO pictureDetail : algorithmVOList) {
                AlgorithmRequestFile requestFile = BeanUtil.copyProperties(commonRequestInfo, AlgorithmRequestFile.class);
                AllcoreFileVO attach = attachMap.get(pictureDetail.getFileGuid());
                List<AlgorithmRequestFileDetail> fileDetailList = new ArrayList<AlgorithmRequestFileDetail>() {{
                    add(AlgorithmRequestFileDetail.builder()
                            .fileId(pictureDetail.getFileGuid())
                            .filePath(attach != null ? attach.getStaticPathNet() : "")
                            .build());
                }};
                requestFile.setFileList(fileDetailList);
                requestFiles.add(requestFile);
            }
        }
        return requestFiles;
    }

    /**
     * （封装算法格式图片路径list）
     *
     * @param strategyDetailMap
     * @param fanPicList
     * @param linePicList
     * @param pvPicList
     * @param map
     * @param shapesPre
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/13 11:16
     */
    private String buildImgPathList(StrategyDetailVO strategyDetailMap, List<InspectionPictureAlgorithmVO> fanPicList,
                                    List<InspectionPictureAlgorithmVO> linePicList, List<InspectionPictureAlgorithmVO> pvPicList,
                                    Map<String, String> map, String shapesPre) {
        StringBuffer imgPathListBuff = new StringBuffer();
        switch (strategyDetailMap.getDeviceType()) {
            case TMS_LINE:
                linePicList.forEach(e -> {
                    imgPathListBuff.append(e.getFilePath());
                    imgPathListBuff.append("-+-");
                    imgPathListBuff.append(e.getId());
                    imgPathListBuff.append(",");
                });
                break;
            case FAN:
                fanPicList.forEach(e -> {
                    if (StringUtil.isBlank(e.getGroupId())) {
                        e.setGroupId(CommonUtil.generateUuid());
                    }
                    imgPathListBuff.append(e.getFilePath());
                    imgPathListBuff.append("-+-");
                    // 星罗厂家要求风机图片属于可见光加上_Z
                    imgPathListBuff.append(e.getGroupId() + "_Z");
                    imgPathListBuff.append("-+-");
                    imgPathListBuff.append(e.getId());
                    imgPathListBuff.append(",");
                });
                break;
            case PV:
                pvPicList.forEach(e -> {
                    if (StringUtil.isBlank(e.getGroupId())) {
                        e.setGroupId(CommonUtil.generateUuid());
                    }
                    imgPathListBuff.append(e.getFilePath());
                    imgPathListBuff.append("-+-");
                    // 判断是红外_T 还是 可见光_Z
                    imgPathListBuff.append(e.getGroupId() + CommonUtil.getPicType(e.getOriginalName()));
                    imgPathListBuff.append("-+-");
                    imgPathListBuff.append(e.getId());
                    imgPathListBuff.append(",");
                });
                map.put("location", sourceProperties.getShapesSystem() + StringPool.UNDERSCORE + shapesPre + "_shapes");
                break;
            default:
                break;
        }
        // 去除最后一位分隔符
        if (imgPathListBuff.length() != 0) {
            imgPathListBuff.deleteCharAt(imgPathListBuff.length() - 1);
        }
        return imgPathListBuff.toString();
    }

    /**
     * （调用算法的数据预处理）
     *
     * @param pictures
     * @param
     * @return void
     * <AUTHOR>
     * @date 2023/11/13 10:57
     */
    private void algorithmIdentifyPre(List<InspectionPicture> pictures,
                                      List<InspectionPictureAlgorithmVO> algorithmVOList) {
        List<String> fileGuidList =
                pictures.stream().map(InspectionPicture::getFileGuid).distinct().collect(Collectors.toList());
        R<List<AllcoreFileVO>> listFileData = ossClient.getFilesDetail(fileGuidList);
        if (listFileData.isSuccess()) {
            for (AllcoreFileVO fileVO : listFileData.getData()) {
                for (InspectionPicture picture : pictures) {
                    if (picture.getFileGuid().equals(fileVO.getFileGuid())) {
                        InspectionPictureAlgorithmVO algorithmVO = new InspectionPictureAlgorithmVO();
                        algorithmVO.setId(picture.getId());
                        algorithmVO.setFileGuid(picture.getFileGuid());
                        algorithmVO.setFilePath(fileVO.getStaticPath());
                        algorithmVO.setOriginalName(fileVO.getOriginalName());
                        algorithmVO.setDeviceType(picture.getDeviceType());
                        algorithmVO.setGroupId(picture.getGroupId());
                        algorithmVO.setDeptCode(picture.getDeptCode());
                        algorithmVOList.add(algorithmVO);
                    }
                }
            }
        }
        // 分组
        for (InspectionPictureAlgorithmVO pic1 : algorithmVOList) {
            if (StringUtil.isBlank((pic1.getGroupId()))) {
                for (InspectionPictureAlgorithmVO pic2 : algorithmVOList) {
                    // 不和自己比较
                    if (pic1.getId().equals(pic2.getId())) {
                        continue;
                    }
                    // 根据名称判断是否是同一组
                    if (CommonUtil.isGroup(pic1.getOriginalName(), pic2.getOriginalName())) {
                        // 判断是否是有组id，没有则加上，有就沿用
                        if (StringUtil.isBlank(pic2.getGroupId())) {
                            pic2.setGroupId(CommonUtil.generateUuid());
                        }
                        pic1.setGroupId(pic2.getGroupId());
                        // 更新 groupId
                        LambdaUpdateWrapper<InspectionPicture> updateWrapper = new LambdaUpdateWrapper();
                        updateWrapper.set(InspectionPicture::getGroupId, pic1.getGroupId()).in(InspectionPicture::getId, CollUtil.newArrayList(pic1.getId(), pic2.getId()));
                        inspectionPictureMapper.update(null, updateWrapper);
                    }
                }
            }

        }
    }

    /**
     * （调用算法之前的校验）
     *
     * @param id
     * @param inspectionTaskId
     * @param taskInfo
     * @param pictures
     * @return void
     * <AUTHOR>
     * @date 2023/11/13 10:54
     */
    private String algorithmIdentifyCheck(String id, String inspectionTaskId, RecognitionTask taskInfo,
                                          List<InspectionPicture> pictures, List<StrategyDetailVO> strategyDetailList, String strategyId) {

        // 缺陷任务id和工单id必须二选一
        if (StringUtil.isAllBlank(id, inspectionTaskId)) {
            throw new ServiceException("缺陷任务id和工单id不能同时为空");
        }
        RecognitionTask taskInfoQuery = null;
        if (StringUtil.isNotBlank(id)) {
            // 判断当前任务是否存在
            taskInfoQuery = getById(id);
        } else if (StringUtil.isNotBlank(inspectionTaskId)) {
            taskInfoQuery = getOne(new LambdaQueryWrapper<RecognitionTask>().eq(RecognitionTask::getInspectionTaskId, id));
        }
        if (taskInfo == null) {
            throw new ServiceException("该任务已不存在,请刷新页面后重试");
        }
        BeanUtil.copyProperties(taskInfoQuery, taskInfo);

        // 获取当前任务对应的图片列表
        /*List<InspectionPicture> list = inspectionPictureService
            .list(new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getRecognitionTaskId, id)
                .eq(InspectionPicture::getPicAlgorithmFlg, BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode()));*/
        List<InspectionPicture> list = inspectionPictureService
                .list(new LambdaQueryWrapper<InspectionPicture>()
                        .eq(InspectionPicture::getRecognitionTaskId, id)
                        .and(wrapper -> wrapper
                                .eq(InspectionPicture::getPicAlgorithmFlg, BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode())
                                .or(innerWrapper -> innerWrapper
                                        .eq(InspectionPicture::getPicAlgorithmFlg, BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode())
                                        .eq(InspectionPicture::getPicDefectFlg, BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode())
                                )
                        )
                );
        if (CollectionUtil.isEmpty(list)) {
            throw new ServiceException("当前任务图片已经都识别完成,请刷新页面后重试");
        }
        pictures.addAll(list);
        Strategy strategy =
                strategyMapper.selectOne(new LambdaQueryWrapper<Strategy>().eq(Strategy::getIsEnable, StringPool.YES));
        // 获取需要执行的算法
        List<StrategyDetailVO> strategyDetails =
                strategyDetailMapper.getStrategyDetailAndAlgorithmManufacturersByStrategyId(strategy.getId(), taskInfo.getDeviceType());
        if (CollectionUtil.isEmpty(strategyDetails)) {
            throw new ServiceException("没有获取到策略信息");
        }
        if (strategyDetails.size() > 1) {
            throw new ServiceException("当前策略下存在多个算法,请联系管理员");
        }
        strategyDetailList.addAll(strategyDetails);
        return taskInfo.getDeptCode();
    }

    /**
     * （开始调用算法）
     *
     * @param strategyDetailMap
     * @param map
     * @return boolean
     * <AUTHOR>
     * @date 2023/11/01 16:55
     */
    private boolean assembleAlgorithm(StrategyDetailVO strategyDetailMap, Map<String, String> map)
            throws ServiceException {

        // http://***********:8000/api/main-server/recognitiontask/getAlgorithmBallback
        map.put("post_url", defectProperties.getAlgorithmPostUrl());
        map.put("model_list", strategyDetailMap.getStrategyDetailModel());

        map.put("algorithm_type", strategyDetailMap.getAlgorithmType());
        // 进行算法调用
        log.info(">>>>地址: {}", strategyDetailMap.getAlgorithmAddress());
        log.info(">>>>模型列表: {}", map.get("model_list"));
        log.info(">>>>回调地址: {}", map.get("post_url"));
        log.info(">>>>参数: {}", JSON.toJSONString(map));
        String result = "";
        if (IS_OWN.equals(strategyDetailMap.getAlgorithmImageName())) {
            result = invokingOwnAlgorithm(strategyDetailMap.getAlgorithmAddress(), map);
        } else {
            // 表单的形式调用别的算法
            result = invokingAlgorithm(strategyDetailMap.getAlgorithmAddress(), map);
        }
        log.info(">>>>响应: {}", result);
        // 判断算法调用是否成功
        String ok = "OK";
        if (StringUtil.isBlank(result) || !ok.equals(result)) {
            return false;
        }
        return true;
    }

    /**
     * （汉创算法调用）
     *
     * @param callUrl
     * @param map
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/01 16:55
     */
    private String invokingOwnAlgorithm(String callUrl, Map<String, String> map) {
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(callUrl);
        String res = "";
        String code200 = "200";
        try {
            post.addHeader("Content-Type", "application/json; charset=utf-8");
            String jsonentity = JSON.toJSONString(map);
            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            post.setEntity(entity);
            HttpResponse response = client.execute(post);
            res = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(res);
            res = (String) jsonObject.get("status");
            if (StringUtil.isBlank(res)) {
                res = jsonObject.get("code") + "";
                if (code200.equals(res)) {
                    res = "OK";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * （第三方算法调用）
     *
     * @param callUrl
     * @param map
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/01 16:56
     */
    private String invokingAlgorithm(String callUrl, Map<String, String> map) throws ServiceException {
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(callUrl);
        String res = "";
        String code200 = "200";
        try {
            post.addHeader("Content-Type", "application/x-www-form-urlencoded");
            List<NameValuePair> params = Lists.newArrayList();
            params.add(new BasicNameValuePair("img_path_list", map.get("img_path_list")));
            params.add(new BasicNameValuePair("post_url", map.get("post_url")));
            String modelList = "model_list";
            if (StringUtil.isNotBlank(map.get(modelList))) {
                params.add(new BasicNameValuePair("model_list", map.get("model_list")));
            }
            String location = "location";
            if (StringUtil.isNotBlank(map.get(location))) {
                params.add(new BasicNameValuePair(location, map.get(location)));
            }
            params.add(new BasicNameValuePair("batch_id", map.get("batch_id")));
            params.add(new BasicNameValuePair("algorithm_type", map.get("algorithm_type")));
            post.setEntity(new UrlEncodedFormEntity(params, StringPool.UTF_8));
            HttpResponse response = client.execute(post);
            // 识别成功返回参数 {"status":"OK"}
            res = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(res);
            res = (String) jsonObject.get("status");
            if (StringUtil.isBlank(res)) {
                res = jsonObject.get("code") + "";
                if (code200.equals(res)) {
                    res = "OK";
                }
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            throw new ServiceException("调用算法失败");
        } catch (ClientProtocolException e) {
            e.printStackTrace();
            throw new ServiceException("调用算法失败");
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("调用算法失败");
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R doAlgorithm(String algorithmResult) throws ServiceException {

        // 解析算法回调结果
        TxAlgorithmResultVO txAlgorithmResultVO;
        // 暂时没做公司算法与第三方算法的区分，现根据公司算法固定会多返的这三个字段判断是不是走的公司的算法
        if (algorithmResult.contains(STATUS) && algorithmResult.contains(DATA) && algorithmResult.contains(MESSAGE)) {
            OwnAlgorithmResultVO ownAlgorithmResultVO =
                    JSONObject.parseObject(algorithmResult, OwnAlgorithmResultVO.class);
            txAlgorithmResultVO = ownAlgorithmResultVO.getData();
            // 算法识别失败直接返回
            if (!ownAlgorithmResultVO.getStatus()) {
                return R.fail("算法识别返回码：" + ownAlgorithmResultVO.getMessage());
            }
        } else {
            txAlgorithmResultVO = JSONObject.parseObject(algorithmResult, TxAlgorithmResultVO.class);
        }
        // 缺陷识别任务GUID
        String recognitionTaskId = txAlgorithmResultVO.getBatchId();
        // 缺陷图片明细guid
        // 测试的时候 txAlgorithmResultVO.getFileName()值是 878f5c5a0df047129b0045bedcdbbb91.jpg
        String inspectionPictureId = FileUtil.getNameWithoutExtension(txAlgorithmResultVO.getFileName());
        // 算法类型
        String algorithmType = txAlgorithmResultVO.getAlgorithmType();
        // 获取图片明细信息
        InspectionPicture inspectionPicture = inspectionPictureService
                .getOne(new QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getId, inspectionPictureId));
        List<List<Object>> alarms = txAlgorithmResultVO.getAlarms();
        RecognitionTask recognitionTask =
                getOne(new QueryWrapper<RecognitionTask>().lambda().eq(RecognitionTask::getId, recognitionTaskId));
        if (!BizDictEnum.RECOGNITION_STATUS_TO_IDENTIFY.getCode().equals(recognitionTask.getRecognitionTaskStatus())) {
            return R.fail("操作失败,该任务状态不可识别");
        }
        // 修改图片信息，保存缺陷信息
        if (alarms.size() > 0) {
            /**
             * 根据缺陷code编码和算法厂家guid查出对应的缺陷编码数据
             */
            handleAlgorithmResult(recognitionTask, inspectionPicture, alarms, algorithmType);
            log.info("将图片变更为已标注");
            // 已标注图片
            inspectionPicture.setPicAlgorithmFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
            inspectionPicture.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
        } else {
            // 正常图片
            inspectionPicture.setPicAlgorithmFlg(BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode());
            inspectionPicture.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode());
        }
        inspectionPicture.setUpdateTime(new Date());
        inspectionPicture.setUpdateUser(recognitionTask.getCreateUser());
        inspectionPictureService.updateById(inspectionPicture);
        // 判断该任务下是否还有未识别的图片
        Long unlabeledLong = inspectionPictureService
                .count(new QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getPicAlgorithmFlg, "unlabeled")
                        .eq(InspectionPicture::getRecognitionTaskId, recognitionTaskId)
                        .eq(InspectionPicture::getBindFlag, StringPool.YES));
        log.info("该任务下未识别的图片数量为:{}", unlabeledLong);
        // 图片全部识别完完毕
        if (unlabeledLong < 1) {
            // 算法识别完成后，无论巡检任务是否办结，都设置为"已识别"状态
            recognitionTask.setRecognitionTaskStatus(BizDictEnum.RECOGNITION_STATUS_IDENTIFIED.getCode());
            updateById(recognitionTask);

            // 如果巡检任务已办结，生成消缺任务（但不改变识别任务状态）

//            InspectionTask inspectionTask = inspectionTaskService.getById(recognitionTask.getInspectionTaskId());
            InspectionTask inspectionTask = inspectionTaskMapper.selectById(recognitionTask.getInspectionTaskId());
            // 判断巡检任务是否办结 如果已办结 且 识别完成 则变成 任务完成
            if (inspectionTask.getInspectionTaskStatus().equals(THREE)) {
                // 查询出图片的所有缺陷 不筛选是否审核
                log.info("=============查询出图片的所有缺陷");
                List<InspectionPictureTagging> taggingIds;
                //过滤掉实际相同的可见光缺陷框
                //遍历 如果defect_key 在 light_defect_key中存在 则舍弃
                if (StringUtil.equals(PV, recognitionTask.getDeviceType())) {
                    taggingIds =
                            inspectionPictureTaggingMapper.getPvDefectTaggingIds(recognitionTask.getId(), StringPool.EMPTY,
                                    BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
                    // 过滤可见光缺陷
                    this.filterLightDefect(taggingIds);
                    taggingIds = this.filterRepeatDefect(taggingIds);
                } else {
                    taggingIds =
                            inspectionPictureTaggingMapper.getDefectTaggingIds(recognitionTaskId, StringPool.EMPTY,
                                    BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
                }
                log.info("=============生成消缺任务");
                RemoveTaskDTO removeTask = new RemoveTaskDTO();
                removeTask.setDeptCode(recognitionTask.getDeptCode());
                removeTask.setCreateUser(recognitionTask.getCreateUser());
                removeTask.setCreateDept(recognitionTask.getCreateDept());
                removeTask.setRemoveTaskName(recognitionTask.getInspectionTaskNo());
                removeTask.setInspectionTaskNo(recognitionTask.getInspectionTaskNo());
                removeTask.setDeviceType(recognitionTask.getDeviceType());
                removeTask.setInspectionTaskId(recognitionTask.getInspectionTaskId());
                removeTask.setInspectionPictureTaggingIds(taggingIds.stream().map(InspectionPictureTagging::getId).collect(Collectors.toList()));
                //根据设备类型设置消缺任务初始状态：光伏设备为待下发，其他设备为执行中
                if (BizDictEnum.DEVICE_TYPE_PV.getCode().equals(recognitionTask.getDeviceType())) {
                    removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_TO_SEND.getCode());
                } else {
                    removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
                }
                removeTaskService.saveRemoveTask(removeTask);
                //新增告警记录
                addAlarmInfos(taggingIds);
            }

            log.info("=============策略使用次数+1");

            // 策略使用次数+1
            strategyMapper.updateUsageTimes(recognitionTask.getStrategyId());
        }
        return R.success("算法结果存储成功");
    }

    /**
     * 新增告警记录
     *
     * @param taggingIds
     */
    private void addAlarmInfos(List<InspectionPictureTagging> taggingIds) {
        if (taggingIds != null && !taggingIds.isEmpty()) {
            List<AlarmInfo> alarmInfoList = new ArrayList<>();
            // 提前获取当前时间，保持一致性
            LocalDateTime now = DateUtil.fromDate(DateUtil.now());

            // 按照 deviceId 分组
            Map<String, List<InspectionPictureTagging>> groupedByDevice = taggingIds.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(tagging -> tagging.getDeviceId()));

            groupedByDevice.forEach((deviceId, groupList) -> {
                // 取一个示例对象用于填充通用字段
                //InspectionPictureTagging example = groupList.get(0);

                /*String defectName = groupList.stream()
                        .map(InspectionPictureTagging::getDefectName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(";"));*/
                groupList.forEach(tag->{
                    AlarmInfo alarmInfo = AlarmInfo.builder()
                            .deptId(tag.getDeptCode())
                            .deviceId(deviceId)
                            .deviceType(tag.getDeviceType())
                            .alarmSource("defect_identification")
                            .alarmType("mission_type")
                            //.alarmContent(defectName)
                            .alarmContent(tag.getDefectName())
                            .alarmLevel(tag.getDefectLevel())
                            .alarmStatus(2)
                            .inspectionTaskId(tag.getInspectionTaskId())
                            .inspectionPictureTaggingId(tag.getId())
                            .alarmTime(now)
                            .createTime(now)
                            .updateTime(now)
                            .build();

                    alarmInfoList.add(alarmInfo);
                });
            });

            alarmClient.saveBatch(alarmInfoList);
        }
    }


    /**
     * （遍历 如果defect_key 在 light_defect_key中存在 则舍弃）
     *
     * @param taggingIds
     * @return void
     * <AUTHOR>
     * @date 2024/01/18 15:58
     */
    private void filterLightDefect(List<InspectionPictureTagging> taggingIds) {
        Iterator<InspectionPictureTagging> iterator = taggingIds.iterator();
        while (iterator.hasNext()) {
            InspectionPictureTagging currentObj = iterator.next();

            for (int i = 0; i < taggingIds.size(); i++) {
                if (!currentObj.equals(taggingIds.get(i)) && currentObj.containsFieldInAnotherField(taggingIds.get(i))) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    public List<InspectionPictureTagging> filterRepeatDefect(List<InspectionPictureTagging> entityList) {
        return entityList.stream()
                .collect(
                        Collectors
                                .toMap(
                                        entity -> Arrays.asList(entity.getDefectDescription(), entity.getPvComponentId(),
                                                entity.getPvComponentName()),
                                        entity -> entity, (existingEntity, newEntity) -> existingEntity))
                .values().stream().collect(Collectors.toList());
    }

    /**
     * 根据缺陷code编码和算法厂家guid查出对应的缺陷编码数据
     */
    public void handleAlgorithmResult(RecognitionTask recognitionTask, InspectionPicture inspectionPicture,
                                      List<List<Object>> alarms, String algorithmType) throws ServiceException {
        try {
            InspectionPictureVO inspectionPictureVO = BeanUtil.copy(inspectionPicture, InspectionPictureVO.class);
            // 是否结构化
            boolean isStruct =
                    BizDictEnum.DEFECT_PIC_TYPE_UN_STRUCT.getCode().equals(inspectionPictureVO.getDefectPicType()) ? true
                            : false;
            List<InspectionPictureTaggingVO> inspectionPictureTaggings = Lists.newArrayList();
            InspectionPictureTaggingVO taggingVO = null;
            String isZ = StringPool.NO;
            R<AllcoreFileVO> rstR = ossClient.getFileDetail(inspectionPictureVO.getFileGuid());
            if (rstR.isSuccess()) {
                inspectionPictureVO.setFilePath(rstR.getData().getStaticPath());
                isZ = rstR.getData().getIsZ();
            }
            for (List<Object> objects : alarms) {
                /**
                 * 算法组装缺陷
                 */
                taggingVO =
                        assembleAlgorithmTagging(taggingVO, recognitionTask, inspectionPictureVO.getId(), inspectionPictureVO.getDeviceId(), objects, algorithmType);
                inspectionPictureTaggings.add(taggingVO);
            }
            // 过滤重复数据
            inspectionPictureTaggings = filterTagging(inspectionPictureTaggings, inspectionPictureVO.getId());
            // 没有新增的直接返回
            if (inspectionPictureTaggings.size() == 0) {
                return;
            }
            /**
             * 判断是否是光伏,光伏缺陷获取组件guid
             */
            if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType)) {
                // 根据光伏组件名称重置光伏组件guid
                inspectionPictureTaggings =
                        setDefectDeviceGuid(inspectionPictureTaggings, recognitionTask.getDeptCode(), inspectionPicture);
            }
            // 如果非光伏类型，则巡检图片标注状态改为未消缺
            if (!PV.equalsIgnoreCase(algorithmType)) {
                inspectionPictureTaggings = inspectionPictureTaggings.stream().peek(tagging -> {
                    tagging.setEliminateStatus(BizDictEnum.TAGGING_TYPE_HAVE_TO_PUSH.getCode());
                }).collect(Collectors.toList());
            }

            // 一次性将所有缺陷框画在一张图上,收集缺陷框数据
            List<Map<String, Integer>> cordMapList = Lists.newArrayList();
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 原图
            String fileImagePath = null;
            //光伏可见光的 不需要画框
            File paintFile = null;
            if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                fileImagePath = createPicture(inspectionPictureVO.getFilePath());
                paintFile = new File(fileImagePath);
            }

            URL url = new URL(inspectionPictureVO.getFilePath());

            // 上传缺陷标注小图
            for (InspectionPictureTaggingVO pictureTagging : inspectionPictureTaggings) {
                int xMin = pictureTagging.getXmin().intValue();
                int xMax = pictureTagging.getXmax().intValue();
                int yMin = pictureTagging.getYmin().intValue();
                int yMax = pictureTagging.getYmax().intValue();

                int xBigMin = null != pictureTagging.getXbigmin() ? pictureTagging.getXbigmin().intValue() : 0;
                int xBigMax = null != pictureTagging.getXbigmax() ? pictureTagging.getXbigmax().intValue() : 0;
                int yBigMin = null != pictureTagging.getYbigmin() ? pictureTagging.getYbigmin().intValue() : 0;
                int yBigMax = null != pictureTagging.getYbigmax() ? pictureTagging.getYbigmax().intValue() : 0;

                cordMapList = Lists.newArrayList();
                Map<String, Integer> cordMap = Maps.newHashMap();
                cordMap.put("x", xMin);
                cordMap.put("y", yMin);
                cordMap.put("width", xMax - xMin);
                cordMap.put("height", yMax - yMin);
                cordMapList.add(cordMap);

                if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                    //已有
                } else {
                    try (InputStream in = (url.openStream())) {
                        fileImagePath = createPicture(in);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    paintFile = paint(fileImagePath, cordMapList, algorithmType);
                }
                // 创建缺陷图，上传缺陷图，删除缺陷图
                Map<String, String> map = upDefectPicture(paintFile, xMin, yMin, xMax, yMax, isStruct, xBigMin, yBigMin,
                        xBigMax, yBigMax, algorithmType);
                pictureTagging.setFileGuid(map.get("defectTagFile"));
                pictureTagging.setBigFileGuid(map.get("defectTagBigFile"));
                pictureTagging.setDefectKey(xMin + "_" + yMin + "_" + xMax + "_" + yMax);

                if (Func.isNotEmpty(pictureTagging.getXbigminLight())) {
                    int xBigMinLight = pictureTagging.getXbigminLight().intValue();
                    int xBigMaxLight = pictureTagging.getXbigmaxLight().intValue();
                    int yBigMinLight = pictureTagging.getYbigminLight().intValue();
                    int yBigMaxLight = pictureTagging.getYbigmaxLight().intValue();
                    pictureTagging.setLightDefectKey(xBigMinLight + "_" + yBigMinLight + "_" + xBigMaxLight + "_" + yBigMaxLight);
                }
                if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                    //已有
                } else {
                    paintFile.delete();
                }
                pictureTagging.setIsZ(isZ);
            }

            if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                paintFile.delete();
            } else {
                //已删
            }
            stopwatch.stop();
            log.info("========================>>>>>>>>>>>>>>>>>>>>>>>>>截图时间：{}",
                    stopwatch.elapsed(TimeUnit.MILLISECONDS));
            // 新增缺陷标注明细数据
            List<InspectionPictureTagging> pictureTaggings =
                    BeanUtil.copy(inspectionPictureTaggings, InspectionPictureTagging.class);
            inspectionPictureTaggingService.saveBatch(pictureTaggings);
            // 上传结束,删除带缺陷框的原图
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("处理算法结果失败");
        }
    }

    /**
     * 根据缺陷code编码和算法厂家guid查出对应的缺陷编码数据
     */
    public int handleAlgorithmResultV2(RecognitionTask recognitionTask, InspectionPicture inspectionPicture,
                                        List<AlgorithmResultDefectDetail> defectList, String algorithmType) throws ServiceException {
        try {
            InspectionPictureVO inspectionPictureVO = BeanUtil.copy(inspectionPicture, InspectionPictureVO.class);
            // 是否结构化
            boolean isStruct =
                    BizDictEnum.DEFECT_PIC_TYPE_UN_STRUCT.getCode().equals(inspectionPictureVO.getDefectPicType()) ? true
                            : false;
            List<InspectionPictureTaggingVO> inspectionPictureTaggings = Lists.newArrayList();
            InspectionPictureTaggingVO taggingVO = null;
            String isZ = StringPool.NO;
            R<AllcoreFileVO> rstR = ossClient.getFileDetail(inspectionPictureVO.getFileGuid());
            if (rstR.isSuccess()) {
                inspectionPictureVO.setFilePath(rstR.getData().getStaticPath());
                isZ = rstR.getData().getIsZ();
            }
            // 获取图片对应的工单包含的设备
            List<InspectionDeviceDetail> list = inspectionDeviceDetailService.list(new LambdaQueryWrapper<InspectionDeviceDetail>()
                    .select(InspectionDeviceDetail::getDeviceId)
                    .eq(InspectionDeviceDetail::getInspectionTaskId, inspectionPicture.getInspectionTaskId()));
            List<String> inspectionDeviceIds = list.stream().map(InspectionDeviceDetail::getDeviceId).collect(Collectors.toList());
            log.info("巡视图片:{}对应的工单设备列表为:{}",inspectionPicture.getId(),inspectionDeviceIds);
            for (AlgorithmResultDefectDetail defectDetail : defectList) {
                /**
                 * 光伏异常处理：工单对应设备id 与 缺陷重置设备id 不匹配时，剔除该缺陷
                 */
                if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType)){
                    if(!checkAndRemoveDefect(defectDetail,inspectionDeviceIds,recognitionTask.getDeptCode())){
                        continue;
                    }
                }
                /**
                 * 算法组装缺陷
                 */
                taggingVO =
                        assembleAlgorithmTaggingV2(taggingVO, recognitionTask, inspectionPictureVO.getId(), inspectionPictureVO.getDeviceId(), defectDetail, algorithmType);
                taggingVO.setGroupId(inspectionPictureVO.getGroupId());
                inspectionPictureTaggings.add(taggingVO);
                if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType)) {
                    String groupId = taggingVO.getGroupId();
                    if (StrUtil.isNotBlank(defectDetail.getVisPosition()) && !StrUtil.equals(defectDetail.getVisPosition(), "[]") && StrUtil.isNotBlank(groupId)) {
                        int xBigMinLight = taggingVO.getXbigminLight().intValue();
                        int xBigMaxLight = taggingVO.getXbigmaxLight().intValue();
                        int yBigMinLight = taggingVO.getYbigminLight().intValue();
                        int yBigMaxLight = taggingVO.getYbigmaxLight().intValue();
                        String checkflg = xBigMinLight + "_" + yBigMinLight + "_" + xBigMaxLight + "_" + yBigMaxLight;
                        LambdaQueryWrapper<InspectionPictureTagging> checkIsZWrapper = new LambdaQueryWrapper<>();
                        checkIsZWrapper
                                .eq(InspectionPictureTagging::getDefectKey, checkflg)
                                .eq(InspectionPictureTagging::getGroupId, groupId)
                                .eq(InspectionPictureTagging::getInspectionTaskId, taggingVO.getInspectionTaskId())
                                .eq(InspectionPictureTagging::getPvComponentName, taggingVO.getPvComponentName());
                        Long cnt = inspectionPictureTaggingMapper.selectCount(checkIsZWrapper);
                        if (cnt <= 0) {
                            LambdaUpdateWrapper<InspectionPicture> queryWrapper = new LambdaUpdateWrapper<>();
                            List<InspectionPicture> inspectionPictures = inspectionPictureMapper.selectList(queryWrapper.eq(InspectionPicture::getGroupId, groupId)
                                    .eq(InspectionPicture::getInspectionTaskId, taggingVO.getInspectionTaskId()).ne(InspectionPicture::getId, taggingVO.getInspectionPictureId()));
                            if (ObjectUtil.isNotEmpty(inspectionPictures)) {
                                InspectionPicture oriPic = inspectionPictures.get(0);
                                InspectionPictureTagging isZTagg = BeanUtil.copyProperties(taggingVO, InspectionPictureTagging.class);
                                isZTagg.setXmin(Math.floor(Convert.toDouble(xBigMinLight, 0.0)));
                                isZTagg.setXmax(Math.floor(Convert.toDouble(xBigMaxLight, 0.0)));
                                isZTagg.setYmin(Math.floor(Convert.toDouble(yBigMinLight, 0.0)));
                                isZTagg.setYmax(Math.floor(Convert.toDouble(yBigMaxLight, 0.0)));

                                isZTagg.setXbigmin(Math.floor(Convert.toDouble(xBigMinLight, 0.0)));
                                isZTagg.setXbigmax(Math.floor(Convert.toDouble(xBigMaxLight, 0.0)));
                                isZTagg.setYbigmin(Math.floor(Convert.toDouble(yBigMinLight, 0.0)));
                                isZTagg.setYbigmax(Math.floor(Convert.toDouble(yBigMaxLight, 0.0)));
                                isZTagg.setInspectionPictureId(oriPic.getId());
                                isZTagg.setIsZ("yes");
                                List<PvComponents> pvComponentsList = pvComponentsService.list(new LambdaQueryWrapper<PvComponents>()
                                        .eq(PvComponents::getDeptCode, recognitionTask.getDeptCode()).eq(PvComponents::getDeviceName, isZTagg.getPvComponentName()));
                                log.info("可见光缺陷新增 {}-{}", isZTagg.getId(), isZTagg.getInspectionPictureId());
                                if (ObjectUtil.isNotEmpty(pvComponentsList)) {
                                    PvComponents pvComponents = pvComponentsList.get(0);
                                    R<AllcoreFileVO> oriPicFile = ossClient.getFileDetail(oriPic.getFileGuid());
                                    if (rstR.isSuccess()) {
                                        String fileImagePath = createPicture(oriPicFile.getData().getStaticPath());
                                        File paintFile = new File(fileImagePath);
                                        Map<String, String> map = upDefectPicture(paintFile, xBigMinLight, yBigMinLight, xBigMaxLight, yBigMaxLight, isStruct, xBigMinLight, yBigMinLight,
                                                xBigMaxLight, yBigMaxLight, algorithmType);
                                        isZTagg.setFileGuid(map.get("defectTagFile"));
                                        isZTagg.setBigFileGuid(map.get("defectTagBigFile"));
                                        paintFile.delete();
                                        isZTagg.setDefectKey(xBigMinLight + "_" + yBigMinLight + "_" + xBigMaxLight + "_" + yBigMaxLight);
                                        isZTagg.setPvComponentId(pvComponents.getId());
                                        isZTagg.setDeviceId(pvComponents.getPvAreaId());
                                        inspectionPictureTaggingService.save(isZTagg);
                                        oriPic.setPicAlgorithmFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
                                        oriPic.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
                                        oriPic.setDeviceId(pvComponents.getPvAreaId());
                                        oriPic.setUpdateTime(new Date());
                                        oriPic.setUpdateUser(recognitionTask.getCreateUser());
                                        inspectionPictureMapper.updateById(oriPic);
                                    } else {
                                        throw new ServiceException("可见光插入错误-获取原文件失败");
                                    }
                                }
                            }
                        }
                    }else{
                        inspectionPictureService.update(
                                new LambdaUpdateWrapper<InspectionPicture>().eq(InspectionPicture::getPicAlgorithmFlg,BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode())
                                        .eq(InspectionPicture::getGroupId,groupId).eq(InspectionPicture::getRecognitionTaskId,recognitionTask.getId())
                                        .set(InspectionPicture::getPicAlgorithmFlg,BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode())
                                        .set(InspectionPicture::getPicDefectFlg,BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode())
                                        .set(InspectionPicture::getUpdateTime,new Date()).set(InspectionPicture::getUpdateUser,recognitionTask.getCreateUser()));
                    }
                }
            }
            // 过滤重复数据
            inspectionPictureTaggings = filterTagging(inspectionPictureTaggings, inspectionPictureVO.getId());
            // 没有新增的直接返回
            if (inspectionPictureTaggings.size() == 0) {
                return inspectionPictureTaggings.size();
            }
            /**
             * 判断是否是光伏,光伏缺陷获取组件guid
             */
            if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType)) {
                // 根据光伏组件名称重置光伏组件guid
                inspectionPictureTaggings =
                        setDefectDeviceGuid(inspectionPictureTaggings, recognitionTask.getDeptCode(), inspectionPicture);
            }

            // 如果非光伏类型，则巡检图片标注状态改为未消缺
            if (!PV.equalsIgnoreCase(algorithmType)) {
                inspectionPictureTaggings = inspectionPictureTaggings.stream().peek(tagging -> {
                    tagging.setEliminateStatus(BizDictEnum.TAGGING_TYPE_HAVE_TO_PUSH.getCode());
                }).collect(Collectors.toList());
            }

            // 一次性将所有缺陷框画在一张图上,收集缺陷框数据
            List<Map<String, Integer>> cordMapList = Lists.newArrayList();
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 原图
            String fileImagePath = null;
            //光伏可见光的 不需要画框
            File paintFile = null;
            if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                fileImagePath = createPicture(inspectionPictureVO.getFilePath());
                paintFile = new File(fileImagePath);
            }

            URL url = new URL(inspectionPictureVO.getFilePath());

            // 上传缺陷标注小图
            for (InspectionPictureTaggingVO pictureTagging : inspectionPictureTaggings) {
                int xMin = pictureTagging.getXmin().intValue();
                int xMax = pictureTagging.getXmax().intValue();
                int yMin = pictureTagging.getYmin().intValue();
                int yMax = pictureTagging.getYmax().intValue();

                int xBigMin = null != pictureTagging.getXbigmin() ? pictureTagging.getXbigmin().intValue() : 0;
                int xBigMax = null != pictureTagging.getXbigmax() ? pictureTagging.getXbigmax().intValue() : 0;
                int yBigMin = null != pictureTagging.getYbigmin() ? pictureTagging.getYbigmin().intValue() : 0;
                int yBigMax = null != pictureTagging.getYbigmax() ? pictureTagging.getYbigmax().intValue() : 0;

                cordMapList = Lists.newArrayList();
                Map<String, Integer> cordMap = Maps.newHashMap();
                cordMap.put("x", xMin);
                cordMap.put("y", yMin);
                cordMap.put("width", xMax - xMin);
                cordMap.put("height", yMax - yMin);
                cordMapList.add(cordMap);

                if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                    //已有
                } else {
                    try (InputStream in = (url.openStream())) {
                        fileImagePath = createPicture(in);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    paintFile = paint(fileImagePath, cordMapList, algorithmType);
                }
                // 创建缺陷图，上传缺陷图，删除缺陷图
                Map<String, String> map = upDefectPicture(paintFile, xMin, yMin, xMax, yMax, isStruct, xBigMin, yBigMin,
                        xBigMax, yBigMax, algorithmType);
                pictureTagging.setFileGuid(map.get("defectTagFile"));
                pictureTagging.setBigFileGuid(map.get("defectTagBigFile"));
                pictureTagging.setDefectKey(xMin + "_" + yMin + "_" + xMax + "_" + yMax);

                if (Func.isNotEmpty(pictureTagging.getXbigminLight())) {
                    int xBigMinLight = pictureTagging.getXbigminLight().intValue();
                    int xBigMaxLight = pictureTagging.getXbigmaxLight().intValue();
                    int yBigMinLight = pictureTagging.getYbigminLight().intValue();
                    int yBigMaxLight = pictureTagging.getYbigmaxLight().intValue();
                    if (xBigMinLight + xBigMaxLight + yBigMinLight + yBigMaxLight > 0) {
                        pictureTagging.setLightDefectKey(xBigMinLight + "_" + yBigMinLight + "_" + xBigMaxLight + "_" + yBigMaxLight);
                    }
                }
                if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                    //已有
                } else {
                    paintFile.delete();
                }
                pictureTagging.setIsZ(isZ);
            }

            if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType) && StringPool.YES.equals(isZ)) {
                paintFile.delete();
            } else {
                //已删
            }
            stopwatch.stop();
            log.info("========================>>>>>>>>>>>>>>>>>>>>>>>>>截图时间：{}",
                    stopwatch.elapsed(TimeUnit.MILLISECONDS));
            // 新增缺陷标注明细数据
            List<InspectionPictureTagging> pictureTaggings =
                    BeanUtil.copy(inspectionPictureTaggings, InspectionPictureTagging.class);
            inspectionPictureTaggingService.saveBatch(pictureTaggings);
            return pictureTaggings.size();
            // 上传结束,删除带缺陷框的原图
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("处理算法结果失败");
        }
    }

    private boolean checkAndRemoveDefect(AlgorithmResultDefectDetail defectDetail, List<String> deviceIds, String deptCode) {
        // 根据组件名称获取组件id
        String deviceName = defectDetail.getZjInfo();
        if (StringUtil.isBlank(deviceName)) {
            return false;
        }
        PvComponents one = pvComponentsService.getOne(new LambdaQueryWrapper<PvComponents>()
                .eq(PvComponents::getDeptCode, deptCode).eq(PvComponents::getDeviceName, deviceName));
        if (one != null && deviceIds.contains(one.getPvAreaId())) {
            // 缺陷组件id在工单设备中
            return true;
        }
        log.info("未找到组件名:{}",defectDetail.getZjInfo());
        return false;
    }

    /**
     * 缺陷标注小图上传
     *
     * @param srcImageFile
     * @param
     * @return
     */
    public Map<String, String> upDefectPicture(File srcImageFile, int xMin, int yMin, int xMax, int yMax,
                                               boolean isStruct, int xBigMin, int yBigMin, int xBigMax, int yBigMax, String algorithmType) {
        Map<String, String> map = new HashMap<>(2);


        if (StringUtil.isNotBlank(algorithmType) && "WF".equalsIgnoreCase(algorithmType)) {
            // 缺陷小图放大 长宽乘3
            File detectBigFile =
                    new File(System.getProperty("user.dir") + File.separator + CommonUtil.generateUuid() + ".jpg");

            // 图片缺陷框太小,看不出具体位置,适当放大一点截图大小
            cutBigPicture(srcImageFile, xMin, yMin, xMax, yMax, detectBigFile);
            log.info("======新版风机缺陷小图====");
            // 光伏之外的 不需要放大版图 减小业务性能消耗
            try (FileInputStream inFile = new FileInputStream(srcImageFile);
                 FileInputStream inBigFile = new FileInputStream(detectBigFile)) {
                MultipartFile multipartFile = FileUtils.getMultipartFile(inFile, srcImageFile.getName());
                MultipartFile multipartBigFile = FileUtils.getMultipartFile(inBigFile, detectBigFile.getName());

                R<AllcoreFileVO> rst = ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                        multipartFile, StringPool.NO, "");
                R<AllcoreFileVO> rstBig = ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                        multipartBigFile, StringPool.NO, "");
                //原图+小框
                map.put("defectTagFile", rst.getData().getFileGuid());
                //小框+局部放大
                map.put("defectTagBigFile", rstBig.getData().getFileGuid());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            // 缺陷小图
            File detectFile =
                    new File(System.getProperty("user.dir") + File.separator + CommonUtil.generateUuid() + ".jpg");

            // 图片缺陷框太小,看不出具体位置,适当放大一点截图大小
            cutPicture(srcImageFile, xMin, yMin, xMax, yMax, detectFile);
            if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType)) {
                // 缺陷小图放大 长宽乘3
                File detectBigFile =
                        new File(System.getProperty("user.dir") + File.separator + CommonUtil.generateUuid() + ".jpg");
                // 如果没拿到组串坐标 也用自动放大版裁剪
                if (0 == xBigMin && 0 == yBigMin && 0 == xBigMax && 0 == yBigMax) {
                    cutBigPicture(srcImageFile, xMin, yMin, xMax, yMax, detectBigFile);
                } else {
                    cutPvBigPicture(srcImageFile, xBigMin, yBigMin, xBigMax, yBigMax, detectBigFile);
                }

                try (FileInputStream inFile = new FileInputStream(detectFile);
                     FileInputStream inBigFile = new FileInputStream(detectBigFile)) {
                    MultipartFile multipartFile = FileUtils.getMultipartFile(inFile, detectFile.getName());
                    MultipartFile multipartBigFile = FileUtils.getMultipartFile(inBigFile, detectBigFile.getName());
                    R<AllcoreFileVO> rst = ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                            multipartFile, StringPool.NO, "");
                    R<AllcoreFileVO> rstBig = ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                            multipartBigFile, StringPool.NO, "");
                    map.put("defectTagFile", rst.getData().getFileGuid());
                    map.put("defectTagBigFile", rstBig.getData().getFileGuid());
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        detectFile.delete();
                        detectBigFile.delete();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                // 光伏之外的 不需要放大版图 减小业务性能消耗
                try (FileInputStream inFile = new FileInputStream(detectFile)) {
                    MultipartFile multipartFile = FileUtils.getMultipartFile(inFile, detectFile.getName());
                    R<AllcoreFileVO> rst = ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                            multipartFile, StringPool.NO, "");

                    map.put("defectTagFile", rst.getData().getFileGuid());
                    map.put("defectTagBigFile", rst.getData().getFileGuid());
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        detectFile.delete();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        return map;
    }

    /**
     * 从{@link InputStream}读取字节数组<br>
     * 结束时会关闭{@link InputStream}<br>
     * {@code in}为{@code null}时抛出{@link NullPointerException}
     *
     * @param in
     * @return 字节数组
     * @throws IOException
     */
    public static final byte[] readBytes(InputStream in) throws IOException {
        if (null == in) {
            throw new NullPointerException("the argument 'in' must not be null");
        }
        try {
            int buffSize = Math.max(in.available(), 1024 * 8);
            byte[] temp = new byte[buffSize];
            ByteArrayOutputStream out = new ByteArrayOutputStream(buffSize);
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            return out.toByteArray();
        } finally {
            in.close();
        }
    }

    public static final BufferedImage readMemoryImage1(byte[] imgBytes) throws IOException {
        if (null == imgBytes || 0 == imgBytes.length) {
            throw new NullPointerException("the argument 'imgBytes' must not be null or empty");
        }
        // 将字节数组转为InputStream，再转为MemoryCacheImageInputStream
        ImageInputStream imageInputstream = new MemoryCacheImageInputStream(new ByteArrayInputStream(imgBytes));
        // 直接调用ImageIO.read方法解码
        BufferedImage bufImg = ImageIO.read(imageInputstream);
        if (null == bufImg)
        // 没有能识别此数据的图像ImageReader对象，抛出异常
        {
            throw new IOException("unsupported image format");
        }
        return bufImg;
    }

    public static final BufferedImage readMemoryImage(byte[] imgBytes) throws IOException {
        if (null == imgBytes || 0 == imgBytes.length) {
            throw new NullPointerException("the argument 'imgBytes' must not be null or empty");
        }
        // 将字节数组转为InputStream，再转为MemoryCacheImageInputStream
        ImageInputStream imageInputstream = new MemoryCacheImageInputStream(new ByteArrayInputStream(imgBytes));
        // 获取所有能识别数据流格式的ImageReader对象
        Iterator<ImageReader> it = ImageIO.getImageReaders(imageInputstream);
        // 迭代器遍历尝试用ImageReader对象进行解码
        while (it.hasNext()) {
            ImageReader imageReader = it.next();
            // 设置解码器的输入流
            imageReader.setInput(imageInputstream, true, true);
            // 图像文件格式后缀
            String suffix = imageReader.getFormatName().trim().toLowerCase();
            // 图像宽度
            int width = imageReader.getWidth(0);
            // 图像高度
            int height = imageReader.getHeight(0);
            System.out.printf("format %s,%dx%d\n", suffix, width, height);
            try {
                // 解码成功返回BufferedImage对象
                // 0即为对第0张图像解码(gif格式会有多张图像),前面获取宽度高度的方法中的参数0也是同样的意思
                return imageReader.read(0, imageReader.getDefaultReadParam());
            } catch (Exception e) {
                imageReader.dispose();
                // 如果解码失败尝试用下一个ImageReader解码
            }
        }
        imageInputstream.close();
        // 没有能识别此数据的图像ImageReader对象，抛出异常
        throw new IOException("unsupported image format");
    }

    // 注意：使用BufferedImage操作图片需要将图片加载到内存中，如果图片过大或者多个线程来请求，
    // 很容易发生内存溢出的问题。可以使用ImageMaigck+Im4java来裁剪。可以参考博主另一篇：ImageMagick+Im4Java裁剪图片

    /**
     * JAVA裁剪图片
     *
     * @param srcImageFile  需要裁剪的图片
     * @param destImageFile 裁剪后的图片
     * @return
     */
    public boolean cutPicture(File srcImageFile, int xMin, int yMin, int xMax, int yMax, File destImageFile) {
        // try(InputStream in = Files.newInputStream(srcImageFile.toPath())) {
        try {
            // 老的
            // 使用ImageIO的read方法读取图片
            BufferedImage read = ImageIO.read(srcImageFile);
            int xCoordinateNew = xMin - 10;
            int yCoordinateNew = yMin - 10;
            int widthNew = xMax - xMin + 20;
            int heightNew = yMax - yMin + 20;

            // 老版截图的异常保护
            if (xCoordinateNew < 0) {
                xCoordinateNew = xMin;
            }
            if (yCoordinateNew < 0) {
                yCoordinateNew = yMin;
            }
            if ((widthNew + xCoordinateNew) > read.getWidth()) {
                widthNew = read.getWidth() - xCoordinateNew;
            }
            if ((heightNew + yCoordinateNew) > read.getHeight()) {
                heightNew = read.getHeight() - yCoordinateNew;
            }

            log.info("cutPicture原图大小========={},{}", read.getWidth(), read.getHeight());
            log.info("cutPicture截图属性========={},{},{},{}", xCoordinateNew, yCoordinateNew, widthNew, heightNew);
            BufferedImage image = read.getSubimage(xCoordinateNew, yCoordinateNew, widthNew, heightNew);

            // 获取到文件的后缀名
            String fileName = srcImageFile.getName();
            String formatName = fileName.substring(fileName.lastIndexOf(".") + 1);
            // 使用ImageIO的write方法进行输出
            ImageIO.write(image, formatName, destImageFile);
            // 关闭图片对象资源
            read.flush();
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public boolean cutBigPicture(File srcImageFile, int xMin, int yMin, int xMax, int yMax, File destImageFile) {
        try {
            // 老的
            // 使用ImageIO的read方法读取图片
            BufferedImage read = ImageIO.read(srcImageFile);

            // 调用裁剪方法
            // 新版 长宽x3
            // 以图片宽度为基准 x
            int xCoordinateNew = xMin - (xMax - xMin);
            if (xCoordinateNew < 0) {
                xCoordinateNew = xMin;
            }
            int yCoordinateNew = yMin - (yMax - yMin);
            if (yCoordinateNew < 0) {
                yCoordinateNew = yMin;
            }
            int widthNew = (xMax - xMin) * 3;
            if ((widthNew + xCoordinateNew) > read.getWidth()) {
                widthNew = read.getWidth() - xCoordinateNew;
            }
            int heightNew = (yMax - yMin) * 3;
            if ((heightNew + yCoordinateNew) > read.getHeight()) {
                heightNew = read.getHeight() - yCoordinateNew;
            }

            log.info("cutBigPicture原图大小========={},{}", read.getWidth(), read.getHeight());
            log.info("cutBigPicture截图属性========={},{},{},{}", xCoordinateNew, yCoordinateNew, widthNew, heightNew);
            BufferedImage image = read.getSubimage(xCoordinateNew, yCoordinateNew, widthNew, heightNew);

            // 获取到文件的后缀名
            String fileName = srcImageFile.getName();
            String formatName = fileName.substring(fileName.lastIndexOf(".") + 1);
            // 使用ImageIO的write方法进行输出
            ImageIO.write(image, formatName, destImageFile);
            // 关闭图片对象资源
            read.flush();
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public boolean cutPvBigPicture(File srcImageFile, int xMin, int yMin, int xMax, int yMax, File destImageFile) {
        try {
            // 老的
            // 使用ImageIO的read方法读取图片
            BufferedImage read = ImageIO.read(srcImageFile);

            // 调用裁剪方法
            // 新版 长宽x3
            // 以图片宽度为基准 x
            // 老版
            int xCoordinateNew = xMin;
            int yCoordinateNew = yMin;
            int widthNew = xMax - xMin;
            int heightNew = yMax - yMin;

            // 截图的异常保护
            if (xCoordinateNew < 0) {
                xCoordinateNew = xMin;
            }
            if (yCoordinateNew < 0) {
                yCoordinateNew = yMin;
            }
            if ((widthNew + xCoordinateNew) > read.getWidth()) {
                widthNew = read.getWidth() - xCoordinateNew;
            }
            if ((heightNew + yCoordinateNew) > read.getHeight()) {
                heightNew = read.getHeight() - yCoordinateNew;
            }

            log.info("cutPvBigPicture原图大小========={},{}", read.getWidth(), read.getHeight());
            log.info("cutPvBigPicture截图属性========={},{},{},{}", xCoordinateNew, yCoordinateNew, widthNew, heightNew);
            BufferedImage image = read.getSubimage(xCoordinateNew, yCoordinateNew, widthNew, heightNew);

            // 获取到文件的后缀名
            String fileName = srcImageFile.getName();
            String formatName = fileName.substring(fileName.lastIndexOf(".") + 1);
            // 使用ImageIO的write方法进行输出
            ImageIO.write(image, formatName, destImageFile);
            // 关闭图片对象资源
            read.flush();
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 根据缺陷坐标点画出缺陷框
     *
     * @param
     * @param cordMapList
     * @return
     */
    private File
    paint(String fileImagePath, List<Map<String, Integer>> cordMapList, String algorithmType) {
        log.info("=================cordMapList===大小======{}", cordMapList.size());
        File fileImg = new File(fileImagePath);
        File file = null;
        // 开始画图
        // 分割路径和文件后缀
        String[] img = fileImg.getName().split("\\.");
        if (img.length > 0) {

            BufferedImage image = null;
            // try(InputStream in = Files.newInputStream(fileImg.toPath())) {
            try {
                // 老的
                // 使用ImageIO的read方法读取图片
                image = ImageIO.read(fileImg);

            } catch (IOException e) {
                e.printStackTrace();
            }
            Graphics g1 = image.getGraphics();
            Graphics2D g = (Graphics2D) g1;
            if (StringUtil.isNotBlank(algorithmType) && "WF".equals(algorithmType)) {
                g.setStroke(new BasicStroke(10.0f));

                // 画笔颜色
                g.setColor(Color.red);
            } else {
                g.setStroke(new BasicStroke(2.0f));

                // 画笔颜色
                g.setColor(Color.yellow);
            }


            // 矩形框(原点x坐标，原点y坐标，矩形的长，矩形的宽)
            // 一张图片包含多个缺陷,为避免多次画图影响性能,一次性把所有缺陷框画上
            for (Map<String, Integer> cordMap : cordMapList) {
                g.drawRect(cordMap.get("x"), cordMap.get("y"), cordMap.get("width"), cordMap.get("height"));
            }

            // 作用是销毁程序中指定的图形界面资源，如果在使用了graphics获得windows一些图形资源，而不进行关闭的话，就会造成内存溢出的情况的，导致程序卡死.类似close方法
            g.dispose();

            try {
                fileImg.delete();
                file = new File(System.getProperty("user.dir") + File.separator + CommonUtil.generateUuid() + ".jpg");
                String suffixPng = "PNG";
                if (suffixPng.equals(img[img.length - 1].toUpperCase())) {
                    ImageIO.write(image, "png", file);
                } else {
                    ImageIO.write(image, "jpg", file);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                image.flush();
                // image = null;
            }
        }
        return file;
    }

    /**
     * 生成原图
     *
     * @return
     */
    public String createPicture(String filePath) throws Exception {
        // 生成原图
        String fileName = System.getProperty("user.dir") + "/" + CommonUtil.generateUuid() + ".jpg";
        File file = new File(fileName);
        URL url;
        try {
            url = new URL(filePath);
            InputStream in = (url.openStream());
            // 写入相应的文件
            FileOutputStream out = new FileOutputStream(file);
            // 读取数据
            // 一次性取多少字节
            byte[] bytes = new byte[2048];
            // 接受读取的内容(n就代表的相关数据，只不过是数字的形式)
            int n = -1;
            // 循环取出数据
            while ((n = in.read(bytes, 0, bytes.length)) != -1) {
                out.write(bytes, 0, n);
                // 清除缓存向文件写入数据
                out.flush();
            }
            // 关闭流
            in.close();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("生成原图失败");
        }
        return fileName;
    }

    public String createPicture(InputStream in) throws Exception {
        // 生成原图
        String fileName = System.getProperty("user.dir") + "/" + CommonUtil.generateUuid() + ".jpg";
        File file = new File(fileName);
        try {
            // 写入相应的文件
            FileOutputStream out = new FileOutputStream(file);
            // 读取数据
            // 一次性取多少字节
            byte[] bytes = new byte[2048];
            // 接受读取的内容(n就代表的相关数据，只不过是数字的形式)
            int n = -1;
            // 循环取出数据
            while ((n = in.read(bytes, 0, bytes.length)) != -1) {
                out.write(bytes, 0, n);
                // 清除缓存向文件写入数据
                out.flush();
            }
            // 关闭流
            in.close();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("生成原图失败");
        }
        return fileName;
    }

    /**
     * 光伏缺陷获取组件guid
     *
     * @param
     * @return
     */
    public List<InspectionPictureTaggingVO>
    setDefectDeviceGuid(List<InspectionPictureTaggingVO> inspectionPictureTaggings, String deptCode, InspectionPicture inspectionPicture) {
        // 获取所有缺陷的设备名称
        HashSet<String> deviceNameSet = Sets.newHashSet();
        String deviceName;
        for (InspectionPictureTaggingVO pictureTagging : inspectionPictureTaggings) {
            deviceName = pictureTagging.getPvComponentName();
            if (StringUtil.isNotBlank(deviceName)) {
                deviceNameSet.add(deviceName);
            }
        }
        if (deviceNameSet.size() == 0) {
            return inspectionPictureTaggings;
        }
        // 根据名称获取设备树
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<PvComponents> pvComponentsList = pvComponentsService.list(new LambdaQueryWrapper<PvComponents>()
                .eq(PvComponents::getDeptCode, deptCode).in(PvComponents::getDeviceName, deviceNameSet));

        stopwatch.stop();
        log.info("================================>>>>>>>>>>>>>>>>>>>>>>>>调用设备服务查询光伏时间:{}",
                stopwatch.elapsed(TimeUnit.MILLISECONDS));

        // 提取返回的数据
        Map<String, PvComponents> deviceMap =
                pvComponentsList.stream().collect(Collectors.toMap(o -> o.getDeviceName(), Function.identity()));

        for (InspectionPictureTaggingVO taggingDetailInfo : inspectionPictureTaggings) {
            // 光伏组件名称查询光伏组件guid
            PvComponents pvComponents = deviceMap.get(taggingDetailInfo.getPvComponentName());
            if (pvComponents != null) {
                taggingDetailInfo.setPvComponentId(pvComponents.getId());
                taggingDetailInfo.setDeviceId(pvComponents.getPvAreaId());
                inspectionPicture.setDeviceId(pvComponents.getPvAreaId());
            }
        }
        return inspectionPictureTaggings;
    }

    /**
     * 光伏异常处理：工单对应设备id 与 缺陷重置设备id 不匹配时，剔除该缺陷
     * @param inspectionPictureTaggings
     * @param inspectionPicture
     * @return
     */
    public List<InspectionPictureTaggingVO> removeTags(List<InspectionPictureTaggingVO> inspectionPictureTaggings, InspectionPicture inspectionPicture){
        log.info("剔除前图片每个缺陷对应的设备：{}",inspectionPictureTaggings.stream().map(InspectionPictureTaggingVO::getDeviceId).collect(Collectors.toList()));
        // 工单设备
        List<InspectionDeviceDetail> list = inspectionDeviceDetailService.list(new LambdaQueryWrapper<InspectionDeviceDetail>()
                .select(InspectionDeviceDetail::getDeviceId)
                .eq(InspectionDeviceDetail::getInspectionTaskId, inspectionPicture.getInspectionTaskId()));
        List<String> inspectionDeviceIds = list.stream().map(InspectionDeviceDetail::getDeviceId).collect(Collectors.toList());
        // 剔除不是对应工单设备的缺陷
        if(CollectionUtil.isNotEmpty(inspectionDeviceIds)){
            inspectionPictureTaggings = inspectionPictureTaggings.stream()
                    .filter(tag -> inspectionDeviceIds.contains(tag.getDeviceId()))
                    .collect(Collectors.toList());
            log.info("剔除后图片每个缺陷对应的设备：{}",inspectionPictureTaggings.stream().map(InspectionPictureTaggingVO::getDeviceId).collect(Collectors.toList()));
            return inspectionPictureTaggings;
        }
        return inspectionPictureTaggings;
    }

    /**
     * 过滤已有相同坐标的缺陷
     *
     * @param list
     * @param
     * @return
     */
    private List<InspectionPictureTaggingVO> filterTagging(List<InspectionPictureTaggingVO> list,
                                                           String inspectionPictureId) {
        List<InspectionPictureTaggingVO> resultList = Lists.newArrayList();
        // 获取已有的缺陷
        List<InspectionPictureTagging> pictureTaggingList =
                inspectionPictureTaggingService.list(new LambdaQueryWrapper<InspectionPictureTagging>()
                        .eq(InspectionPictureTagging::getInspectionPictureId, inspectionPictureId));
        // 过滤已有相同坐标的缺陷
        int i;
        for (InspectionPictureTaggingVO pictureTagging : list) {
            i = 0;
            for (InspectionPictureTagging detailInfo : pictureTaggingList) {
                if (pictureTagging.getXmin().intValue() == detailInfo.getXmin().intValue()
                        && pictureTagging.getYmin().intValue() == detailInfo.getYmin().intValue()
                        && pictureTagging.getXmax().intValue() == detailInfo.getXmax().intValue()
                        && pictureTagging.getYmax().intValue() == detailInfo.getYmax().intValue()) {
                    i++;
                }
            }
            if (i == 0) {
                resultList.add(pictureTagging);
            }
        }
        return resultList;
    }

    /**
     * 算法组装缺陷
     *
     * @param tagging
     * @param recognitionTaskInfo
     * @param
     * @param objects
     * @param algorithmType
     * @return
     */
    private InspectionPictureTaggingVO assembleAlgorithmTagging(InspectionPictureTaggingVO tagging,
                                                                RecognitionTask recognitionTaskInfo, String inspectionPictureId, String deviceId, List<Object> objects,
                                                                String algorithmType) {

        // 不能新增租户 如果新增了租户 此接口是白名单 业务字典查的数据未带上租户过滤 getone会查出2
        String defectDictBizCode =
                DictBizCache.getValue(BizDictEnum.ALGORITHM_CODE.getCode(), String.valueOf(objects.get(5)));
        // 根据算法返回类型风光点判断调用方法，添加风光点过滤

        tagging = new InspectionPictureTaggingVO();
        tagging.setInspectionPictureId(inspectionPictureId);
        tagging.setInspectionTaskId(recognitionTaskInfo.getInspectionTaskId());
        tagging.setInspectionTaskNo(recognitionTaskInfo.getInspectionTaskNo());
        tagging.setRecognitionTaskId(recognitionTaskInfo.getId());
        tagging.setDefectDescription(
                StringUtil.isBlank(defectDictBizCode) ? String.valueOf(objects.get(5)) : defectDictBizCode);
        tagging.setDefectName(
                CommonUtil.transDefectCode(tagging.getDefectDescription(), recognitionTaskInfo.getDeviceType()));

        String levelZh = ":一般";
        if (StringUtil.isNotBlank(algorithmType) && "WF".equalsIgnoreCase(algorithmType)) {
            String defectLevel =
                    DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL_FAN.getCode(), CommonUtil.transDefectCode(tagging.getDefectDescription(), recognitionTaskInfo.getDeviceType()));
            tagging.setDefectLevel(defectLevel);
            levelZh = StringPool.COLON + DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), defectLevel);
        } else {
            tagging.setDefectLevel(BizDictEnum.DEFECT_LEVEL_NORMAL.getCode());
        }
        if (StringUtil.isNotBlank(tagging.getDefectName())) {
            tagging.setDefectName(tagging.getDefectName() + levelZh);
        }
        tagging.setAuditStatus(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode());
        tagging.setDefectType(BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
        tagging.setTaggingType(BizDictEnum.TAGGING_TYPE_RECTANGLE.getCode());
        tagging.setIsAlgorithm(StringPool.YES);
        tagging.setMovePosition(StringPool.NO);
        tagging.setEliminateStatus(BizDictEnum.TAGGING_TYPE_NOT_PUSH.getCode());
        tagging.setSystemType(recognitionTaskInfo.getSystemType());
        tagging.setDeptCode(recognitionTaskInfo.getDeptCode());
        tagging.setCreateUser(recognitionTaskInfo.getCreateUser());
        tagging.setCreateDept(recognitionTaskInfo.getCreateDept());
        tagging.setXmin(Math.floor(Double.parseDouble(objects.get(1).toString())));
        tagging.setXmax(Math.floor(Double.parseDouble(objects.get(3).toString())));
        tagging.setYmin(Math.floor(Double.parseDouble(objects.get(2).toString())));
        tagging.setYmax(Math.floor(Double.parseDouble(objects.get(4).toString())));
        tagging.setDeviceType(recognitionTaskInfo.getDeviceType());
        tagging.setDeviceId(deviceId);
        // 判断是否是光伏，如果是，需要获取设备信息,温度
        if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType)) {
            //光伏特殊处理
            handlePvAlg(tagging, objects);
        }

        return tagging;
    }

    /**
     * 算法组装缺陷
     *
     * @param tagging
     * @param recognitionTaskInfo
     * @param
     * @param objects
     * @param algorithmType
     * @return
     */
    private InspectionPictureTaggingVO assembleAlgorithmTaggingV2(InspectionPictureTaggingVO tagging,
                                                                  RecognitionTask recognitionTaskInfo,
                                                                  String inspectionPictureId, String deviceId,
                                                                  AlgorithmResultDefectDetail objects,
                                                                  String algorithmType) {
        CoordinateUtil.Coordinate coordinate = CoordinateUtil.getCoordinate(objects.getCoordinateSet());
        String xMin = coordinate.getXMin();
        String xMax = coordinate.getXMax();
        String yMin = coordinate.getYMin();
        String yMax = coordinate.getYMax();
        log.info("xMin={},xMax={},yMin={},yMax={}", xMin, xMax, yMin, yMax);
        // 不能新增租户 如果新增了租户 此接口是白名单 业务字典查的数据未带上租户过滤 getone会查出2
        String defectDictBizCode =
                DictBizCache.getValue(BizDictEnum.ALGORITHM_CODE.getCode(), String.valueOf(objects.getDefectCode()));
        // 根据算法返回类型风光点判断调用方法，添加风光点过滤

        if (StrUtil.isBlank(defectDictBizCode)) {
            log.error("算法返回的缺陷code配置 {}", objects.getDefectCode());
        }
        tagging = new InspectionPictureTaggingVO();
        tagging.setInspectionPictureId(inspectionPictureId);
        tagging.setInspectionTaskId(recognitionTaskInfo.getInspectionTaskId());
        tagging.setInspectionTaskNo(recognitionTaskInfo.getInspectionTaskNo());
        tagging.setRecognitionTaskId(recognitionTaskInfo.getId());
        tagging.setDefectSize(objects.getObjectSize());
        tagging.setDefectDescription(
                StringUtil.isBlank(defectDictBizCode) ? String.valueOf(objects.getDefectCode()) : defectDictBizCode);
        tagging.setDefectName(
                CommonUtil.transDefectCode(tagging.getDefectDescription(), recognitionTaskInfo.getDeviceType()));
        tagging.setDefectLevel(StringUtil.isNotBlank(objects.getDefectLevels()) ? DictBizCache.getValue(BizDictEnum.ALGORITHM_DEFECT_LEVEL.getCode(), objects.getDefectLevels()) : "normal");
        String levelZh = StringPool.COLON + DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), tagging.getDefectLevel());

        /*if(StringUtil.isNotBlank(algorithmType) && "WF".equalsIgnoreCase(algorithmType)) {
            String defectLevel =
                    DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL_FAN.getCode(), CommonUtil.transDefectCode(tagging.getDefectDescription(), recognitionTaskInfo.getDeviceType()));
            tagging.setDefectLevel(defectLevel);
            levelZh = StringPool.COLON+DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), defectLevel);
        }else {
            tagging.setDefectLevel(BizDictEnum.DEFECT_LEVEL_NORMAL.getCode());
        }*/
        if (StringUtil.isNotBlank(tagging.getDefectName())) {
            tagging.setDefectName(tagging.getDefectName() + levelZh);
        }
        tagging.setAuditStatus(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode());
        tagging.setDefectType(BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
        tagging.setTaggingType(BizDictEnum.TAGGING_TYPE_RECTANGLE.getCode());
        tagging.setIsAlgorithm(StringPool.YES);
        tagging.setMovePosition(StringPool.NO);
        tagging.setEliminateStatus(BizDictEnum.TAGGING_TYPE_NOT_PUSH.getCode());
        tagging.setSystemType(recognitionTaskInfo.getSystemType());
        tagging.setDeptCode(recognitionTaskInfo.getDeptCode());
        tagging.setCreateUser(recognitionTaskInfo.getCreateUser());
        tagging.setCreateDept(recognitionTaskInfo.getCreateDept());
        tagging.setXmin(Math.floor(Double.parseDouble(xMin)));
        tagging.setXmax(Math.floor(Double.parseDouble(xMax)));
        tagging.setYmin(Math.floor(Double.parseDouble(yMin)));
        tagging.setYmax(Math.floor(Double.parseDouble(yMax)));
        tagging.setDeviceType(recognitionTaskInfo.getDeviceType());
        tagging.setDeviceId(deviceId);
        // 判断是否是光伏，如果是，需要获取设备信息,温度
        if (StringUtil.isNotBlank(algorithmType) && PV.equalsIgnoreCase(algorithmType)) {
            //光伏特殊处理
            tagging.setPvComponentName(objects.getZjInfo());
            tagging.setTemperature(objects.getTemperatures());
            if (StrUtil.isNotBlank(objects.getZcInfo()) && !StrUtil.equals(objects.getZcInfo(), "[]")) {
                CoordinateUtil.Coordinate visZcInfo = CoordinateUtil.getCoordinate(objects.getZcInfo());
                tagging.setXbigmin(Math.floor(Convert.toDouble(Convert.toDouble(visZcInfo.getXMin(), 0.0))));
                tagging.setXbigmax(Math.floor(Convert.toDouble(Convert.toDouble(visZcInfo.getXMax(), 0.0))));
                tagging.setYbigmin(Math.floor(Convert.toDouble(Convert.toDouble(visZcInfo.getYMin(), 0.0))));
                tagging.setYbigmax(Math.floor(Convert.toDouble(Convert.toDouble(visZcInfo.getYMax(), 0.0))));
            }
            if (StringUtil.isNotBlank(objects.getVisPosition()) && !StrUtil.equals(objects.getVisPosition(), "[]")) {
                CoordinateUtil.Coordinate visPos = CoordinateUtil.getCoordinate(objects.getVisPosition());
                tagging.setXbigminLight(Math.floor(Convert.toDouble(Convert.toDouble(visPos.getXMin(), 0.0))));
                tagging.setXbigmaxLight(Math.floor(Convert.toDouble(Convert.toDouble(visPos.getXMax(), 0.0))));
                tagging.setYbigminLight(Math.floor(Convert.toDouble(Convert.toDouble(visPos.getYMin(), 0.0))));
                tagging.setYbigmaxLight(Math.floor(Convert.toDouble(Convert.toDouble(visPos.getYMax(), 0.0))));
            }
        }

        return tagging;
    }

    private void handlePvAlg(InspectionPictureTaggingVO tagging, List<Object> objects) {
        int s7 = 7;
        if (objects.get(s7) != null) {
            // 先放光伏组件名称
            // {"status":true,"data":{"start_time":1.699607249218532E9,"end_time":1.69960724946283E9,"width":640,"height":512,"count":2,"batch_id":"222afd0b6d3c43ac8405b86c2f921da6","algorithm_type":PV,"group":"1b3ac10c155249ceb4ec857ab455f0ee_Z","file_id":"e14eab0c10a443c08bec456a1de9e714.JPG","alarms":[[0,73,318,96,360,"hotspot",0.952,"",38.0],[1,552,264,582,309,"hotspot",0.872,"",0.0]]},"message":0}
            tagging.setPvComponentName(String.valueOf(objects.get(7)));
        }
        int s8 = 8;
        if (objects.get(s8) != null) {
            tagging.setTemperature(String.valueOf(objects.get(8)));
        }

        int s9 = 9;
        if (objects.get(s9) != null) {
            List<Object> bigTag = (List<Object>) objects.get(s9);
            if (CollectionUtil.isNotEmpty((bigTag))) {
                tagging.setXbigmin(Math.floor(Double.parseDouble(bigTag.get(0).toString())));
                tagging.setXbigmax(Math.floor(Double.parseDouble(bigTag.get(2).toString())));
                tagging.setYbigmin(Math.floor(Double.parseDouble(bigTag.get(1).toString())));
                tagging.setYbigmax(Math.floor(Double.parseDouble(bigTag.get(3).toString())));
            }
        }
        int s10 = 10;
        if (objects.size() > s10 && objects.get(s10) != null) {
            List<Object> bigTag = (List<Object>) objects.get(s10);
            if (CollectionUtil.isNotEmpty((bigTag))) {
                tagging.setXbigminLight(Math.floor(Double.parseDouble(bigTag.get(0).toString())));
                tagging.setXbigmaxLight(Math.floor(Double.parseDouble(bigTag.get(2).toString())));
                tagging.setYbigminLight(Math.floor(Double.parseDouble(bigTag.get(1).toString())));
                tagging.setYbigmaxLight(Math.floor(Double.parseDouble(bigTag.get(3).toString())));
            }
        }
    }

    @Override
    public void exportReport(String id, HttpServletResponse response) {

        // 图片地址
        String imgPath = System.getProperty("user.dir") + File.separator + System.currentTimeMillis();
        String docxType = "line";
        try {
            if (StringUtil.isBlank(id)) {
                throw new ServiceException("没能获取到taskId");
            }
            RecognitionTask taskInfo = getById(id);
            if (!BizDictEnum.RECOGNITION_STATUS_COMPLETED.getCode().equals(taskInfo.getRecognitionTaskStatus())) {
                throw new ServiceException("当前任务不是已完成状态不可导出");
            }
            // 创建文件夹
            // picture
            File f1 = new File(imgPath);
            if (!f1.exists()) {
                f1.mkdirs();
            }
            /**************************** 报告数据 ***********************************/
            Map<String, Object> dataMap = getWordDataMap(taskInfo, imgPath);
            DeptVO deptVO = sysClient.getDeptInfoByDeptCode(taskInfo.getDeptCode());
            if (deptVO == null) {
                deptVO = new DeptVO();
            }
            // 场站名称
            dataMap.put("czmc", deptVO.getDeptName());
            // 场站地点
            dataMap.put("czdd", deptVO.getRegionName().replaceAll("null", ""));
            // 检测人员
            User user = UserCache.getUser(AuthUtil.getUserId());
            dataMap.put("jcry", user.getRealName());
            // 报告编写人
            dataMap.put("bgbx", user.getRealName());
            // 委托单位联系人
            dataMap.put("wtdwlxr", user.getRealName());
            // 委托单位联系方式
            dataMap.put("wtdwlxfs", user.getPhone());
            // 判断导出的模板类型，默认线路
            String deviceType = taskInfo.getDeviceType();
            if (PV.equals(deviceType)) {
                docxType = "pvnew";
            } else if (FAN.equals(deviceType)) {
                docxType = "fannew";
            }

            XWPFTemplate template;
            HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy();
            if (TMS_LINE.equals(deviceType)) {
                //PoiTabliePolicy render的data就是PICTURELIST
                Configure config = Configure.builder().bind("DEFECTLIST", policy).bind("PICTURELIST", new PoiTabliePolicy()).build();

                template = XWPFTemplate
                        .compile(new ClassPathResource("templates/defect/" + docxType + ".docx").getInputStream(), config)
                        .render(dataMap);
            }else if (FAN.equals(deviceType)){
                Configure config = Configure.builder().bind("defectGroup", policy).bind("deviceDefectList", policy).bind("deviceList", policy).bind("result", policy).build();
                template = XWPFTemplate.compile(new ClassPathResource("templates/defect/" + docxType + ".docx").getInputStream(),config).render(dataMap);
            }else{
                Configure config = Configure.builder().bind("detailzj", policy)
                        .bind("resultList", policy)
                        .bind("detailqy", policy)
                        .bind("zjDefectList", new ListRenderPolicy()).build();
                template = XWPFTemplate.compile(new ClassPathResource("templates/defect/" + docxType + ".docx").getInputStream(),config).render(dataMap);
            }

            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + "out_template.docx" + "\"");
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            template.write(bos);
            bos.flush();
            out.flush();
            PoitlIOUtils.closeQuietlyMulti(template, bos, out);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 递归删除
            try {
                ZipUtil.deleteDir(imgPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取报告数据
     *
     * @param
     * @param imgPath
     * @return
     */
    public Map<String, Object> getWordDataMap(RecognitionTask task, String imgPath) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        // 报告数据
        Map<String, Object> dataMap = Maps.newHashMap();
        // 报告编号
        dataMap.put("gdbh", task.getInspectionTaskNo());
        // 报告日期
        dataMap.put("bgrq", simpleDateFormat.format(new Date()));
        // 检测时间
        dataMap.put("xjsj", simpleDateFormat.format(task.getCreateTime()));

        String deviceType = task.getDeviceType();

        List<DeviceWithParentVO> deviceDetails =
                inspectionDeviceDetailService.getInspectionDeviceDetails(task.getInspectionTaskId(), deviceType);
        List<String> deviceNameList;
        List<String> deviceIdList;
        List<InspectionPictureVO> pictureTagAll = null;
        String deviceNameStr = "";
        //场站基本信息
        StationManagement stationManagement;
        switch (deviceType) {
            case TMS_LINE:
                deviceNameList = deviceDetails.stream().map(e -> e.getParentDeviceName() + e.getDeviceName())
                        .collect(Collectors.toList());
                deviceNameStr = String.join(StringPool.COMMA, deviceNameList) + "输电杆塔";
                // 设备数量
                dataMap.put("sbsl", deviceDetails.size());
                pictureTagAll = inspectionPictureMapper.getLinePictureTagAll(task.getInspectionTaskId());
                break;
            case FAN:
                deviceNameList = deviceDetails.stream().map(e -> e.getDeviceName()).collect(Collectors.toList());
                // 设备数量
                dataMap.put("sbsl", deviceDetails.size());
                deviceNameStr = String.join(StringPool.COMMA, deviceNameList) + "风机";
                deviceIdList = deviceDetails.stream().map(e -> e.getDeviceId()).collect(Collectors.toList());

                //场站基本信息
                stationManagement = stationManagementMapper
                        .selectOne(new LambdaQueryWrapper<StationManagement>().eq(StationManagement::getDeptCode, task.getDeptCode()));

                if (Func.isNotEmpty(stationManagement)) {
                    //额定容量
                    dataMap.put("edrl", stationManagement.getStationInstall());
                    //并网时间
                    dataMap.put("bwsj", simpleDateFormat.format(stationManagement.getConnectionTime()));
                    dataMap.put("czdz", stationManagement.getStationAddress());
                    dataMap.put("dzlx", stationManagement.getPowerStationType());
                }


                //光伏场站 组件组串信息
                fanExtraDataNew(deviceIdList, dataMap, task, stationManagement, deviceDetails);

                break;
            case PV:
                deviceNameList = deviceDetails.stream().map(e -> e.getDeviceName()).collect(Collectors.toList());
                deviceNameStr = String.join(StringPool.COMMA, deviceNameList);

                deviceIdList = deviceDetails.stream().map(e -> e.getDeviceId()).collect(Collectors.toList());

                //场站基本信息
                stationManagement = stationManagementMapper
                        .selectOne(new LambdaQueryWrapper<StationManagement>().eq(StationManagement::getDeptCode, task.getDeptCode()));

                if (Func.isNotEmpty(stationManagement)) {
                    //额定容量
                    dataMap.put("edrl", stationManagement.getStationInstall());
                    //并网时间
                    dataMap.put("bwsj", simpleDateFormat.format(stationManagement.getConnectionTime()));
                    dataMap.put("czdz", stationManagement.getStationAddress());
                    dataMap.put("dzlx", stationManagement.getPowerStationType());
                    dataMap.put("zjzjqj", stationManagement.getComponentAngle());
                    dataMap.put("zjcjgl", stationManagement.getComponentManufacturerPower());
                }


                //光伏场站 组件组串信息
                pvExtraDataNew(deviceIdList, dataMap, task, stationManagement, deviceDetails);


                break;

            default:
                break;
        }

        // 巡检设备
        dataMap.put("xjsb", deviceNameStr);

        if (TMS_LINE.equals(deviceType)) {
            // 丰富图片信息
            List<String> fileGuids =
                    pictureTagAll.stream().map(InspectionPictureVO::getFileGuid).collect(Collectors.toList());
            R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
            morePicInfo(pictureTagAll, rstR);

            List<InspectionPictureTaggingVO> taggingVOList = new ArrayList<>();
            pictureTagAll.forEach(e -> {
                taggingVOList.addAll(e.getInspectionPictureTaggings());
            });

            // 丰富标缺小图信息
            moreTagInfo(taggingVOList, rstR);

            drawPicture(pictureTagAll, imgPath);

            // 设备名称排序后包含缺陷的图片
            dataMap.put("PICTURELIST", pictureTagAll);

            for (int i = 0; i < taggingVOList.size(); i++) {
                taggingVOList.get(i).setId(String.valueOf(i + 1));
                taggingVOList.get(i).setDefectLevel(DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), taggingVOList.get(i).getDefectLevel()));
            }
            // 缺陷表（图片顺序）
            dataMap.put("DEFECTLIST", taggingVOList);

            /**
             * 获取各种缺陷数量
             */
            getDefectNum(dataMap, taggingVOList);

            getPicConfig(dataMap, deviceType, deviceDetails, pictureTagAll);
        }


        return dataMap;
    }

    private void morePicInfo(List<InspectionPictureVO> pictureTagAll, R<List<AllcoreFileVO>> rstR) {
        if (rstR.isSuccess()) {
            pictureTagAll.forEach(e -> {
                rstR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setOriginalName(r.getOriginalName());
                        e.setFilePath(r.getStaticPath());
                        // 图片名称重组
                        String[] names = e.getOriginalName().split(StringPool.SLASH);
                        e.setOriginalName(e.getDeviceName() + "_" + names[names.length - 1].replace("\\", "_"));
                        if (StringUtil.isNotBlank(e.getParentDeviceName())) {
                            e.setOriginalName(e.getParentDeviceName() + "_" + e.getOriginalName());
                        }
                    }
                });
            });
        }
    }

    private void moreTagInfo(List<InspectionPictureTaggingVO> taggingVOList, R<List<AllcoreFileVO>> rstR) {
        List<String> tagFileGuids =
                taggingVOList.stream().map(InspectionPictureTaggingVO::getFileGuid).collect(Collectors.toList());
        R<List<AllcoreFileVO>> tagRstR = ossClient.getFilesDetail(tagFileGuids);
        if (rstR.isSuccess()) {
            // 光伏的时候 -组件名称-温度
            // 其他 -设备名称
            taggingVOList.forEach(e -> {
                // 扩展信息赋值
                if (PV.equals(e.getDeviceType())) {
                    e.setExtendDesc(StringPool.DASH + e.getPvComponentName() + StringPool.DASH + e.getTemperature());
                } else {
                    e.setExtendDesc(StringPool.DASH + e.getDeviceName());
                }
                e.setDefectLevelZh(DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), e.getDefectLevel()));
                tagRstR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setFilePath(r.getStaticPath());
                    }
                });
            });
        }
    }

    private void pvExtraData(List<DeviceWithParentVO> pvDeviceDetails, List<DeviceWithParentVO> pvDefectDeviceDetails, Map<String, Object> dataMap) {
        // 创建一个数值格式化对象
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(2);

        int componentsNum = pvDeviceDetails.stream().map(DeviceWithParentVO::getDeviceId).distinct()
                .collect(Collectors.toList()).size();
        int stringNum = pvDeviceDetails.stream().map(DeviceWithParentVO::getParentDeviceId).distinct()
                .collect(Collectors.toList()).size();


        int defectComponentsNum = pvDefectDeviceDetails.stream().map(DeviceWithParentVO::getDeviceId).distinct()
                .collect(Collectors.toList()).size();
        int defectStringNum = pvDefectDeviceDetails.stream().map(DeviceWithParentVO::getParentDeviceId)
                .distinct().collect(Collectors.toList()).size();
        // 组件数量
        dataMap.put("sbsl", componentsNum);
        // 组串数量
        dataMap.put("zczs", stringNum);
        // 缺陷组件数量
        dataMap.put("zjqxzs", defectComponentsNum);
        // 故障组串数量 有缺陷的组件表示该父级组串也有缺陷
        dataMap.put("gzzcsl", defectStringNum);
        // 故障比例
        if (0 == componentsNum) {
            dataMap.put("pvgzbl", 0.00 + "");
        } else {
            dataMap.put("pvgzbl", numberFormat.format((float) defectComponentsNum / (float) componentsNum * 100));
        }
        // 组串故障比例
        if (0 == stringNum) {
            dataMap.put("gzzcbl", 0.00 + "");
        } else {
            dataMap.put("gzzcbl", numberFormat.format((float) defectStringNum / (float) stringNum * 100));
        }


    }

    private void pvExtraDataNew(List<String> deviceIdList, Map<String, Object> dataMap, RecognitionTask task, StationManagement stationManagement, List<DeviceWithParentVO> deviceDetails) {

        // 创建一个数值格式化对象
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(2);

        List<DeviceWithParentVO> componentsNumList = pvComponentsMapper.componentsNum(deviceIdList);
        int componentsNum = componentsNumList.stream().mapToInt(DeviceWithParentVO::getNum).sum();
        int stringNum = componentsNumList.size();

//        List<DeviceWithParentVO> componentsTagNumList = pvComponentsMapper.componentsTagNum(deviceIdList);
//        int defectComponentsNum = componentsTagNumList.stream().mapToInt(DeviceWithParentVO::getNum).sum();
//        int defectStringNum = componentsTagNumList.size();
        // 组件数量
        dataMap.put("sbsl", componentsNum);
        // 组串数量
        dataMap.put("zczs", stringNum);


        List<DeviceWithParentVO> tagInfos = inspectionPictureTaggingMapper.pvExportTagInfos(task.getId(), deviceIdList);
        if(CollectionUtil.isNotEmpty(tagInfos)){
        	//补充红外图路径和可将光路径
            List<String> fileGuidList =
                    tagInfos.stream().map(DeviceWithParentVO::getBigFileGuid).distinct().collect(Collectors.toList());
            fileGuidList.addAll(tagInfos.stream().map(DeviceWithParentVO::getLightFileGuid).distinct().collect(Collectors.toList()));
            R<List<AllcoreFileVO>> listFileData = ossClient.getFilesDetail(fileGuidList);
            if (listFileData.isSuccess()) {
                // 封装fileMap
                Map<String, AllcoreFileVO> fileMap =
                        listFileData.getData().stream().collect(Collectors.toMap(AllcoreFileVO::getFileGuid, Function.identity()));
                for (DeviceWithParentVO vo : tagInfos) {
                    if (StringUtil.isNotBlank(vo.getBigFileGuid())) {
                        AllcoreFileVO fileVO = fileMap.get(vo.getBigFileGuid());
                        vo.setBigFilePath(fileVO.getStaticPath());
                    }
                    if (StringUtil.isNotBlank(vo.getLightFileGuid())) {
                        AllcoreFileVO fileVO = fileMap.get(vo.getLightFileGuid());
                        vo.setLightFilePath(fileVO.getStaticPath());
                    }
                    //处理手标红外光和可见光(如果是手标的可见光需要交换连接)
                    if (StringUtil.equals(vo.getIsZ(), "yes") && StringUtil.equals(vo.getIsAlgorithm(), "no")) {
                        vo.setLightFilePath(vo.getBigFilePath());
                        vo.setBigFilePath("");
                    }
                }
                List<String> fileGuidList1 = tagInfos.stream().map(DeviceWithParentVO::getPicFileGuid).distinct().collect(Collectors.toList());
                R<List<AllcoreFileVO>> re = ossClient.getFilesDetail(fileGuidList1);
                if (re.isSuccess()) {
                    for (AllcoreFileVO fileVO : re.getData()) {
                        for (DeviceWithParentVO vo : tagInfos) {
                            if (StringUtil.isNotBlank(vo.getPicFileGuid()) && vo.getPicFileGuid().equals(fileVO.getFileGuid())) {
                                vo.setFileName(fileVO.getOriginalName());
                            }
                        }
                    }
                }
            }
        }



        Map<String, List<DeviceWithParentVO>> map = tagInfos.stream().collect(Collectors.groupingBy(DeviceWithParentVO::getPvAreaId));
        Map<String, List<DeviceWithParentVO>> mapSource = componentsNumList.stream().collect(Collectors.groupingBy(DeviceWithParentVO::getPvAreaId));


        List<Map<String, Object>> resultList = new ArrayList<>();
        List<Map<String, Object>> zjDefectList = new ArrayList<>();


        int i = 1;
        int sum1 = 0, sum2 = 0, sum3 = 0, sum4 = 0, sum5 = 0, sum6 = 0, sum7 = 0, sum8 = 0, sum9 = 0, sum10 = 0, sum11 = 0, sum12 = 0, sum13 = 0;
        int defectComponentsNum = 0, defectStringNum = 0;
        for (Map.Entry<String, List<DeviceWithParentVO>> entry : map.entrySet()) {
            List<DeviceWithParentVO> oneInfos = entry.getValue();
            DeviceWithParentVO vo = oneInfos.get(0);

            DeviceWithParentVO areaInfo = deviceDetails.stream().filter(e -> e.getDeviceId().equals(entry.getKey())).findFirst().get();

            //巡检区域缺陷全局图
            R<AllcoreFileVO> rstR = ossClient.getFileDetail(areaInfo.getInspectionReportPic());
            if (rstR.isSuccess()) {
                areaInfo.setInspectionReportPicPath(rstR.getData().getStaticPath());
            }

            //区域组件数量
            int sczjsl = mapSource.get(entry.getKey()).stream().mapToInt(DeviceWithParentVO::getNum).sum();
            //区域组串数量
            int sczcsl = mapSource.get(entry.getKey()).size();

            Map<String, Object> modelInfo = Maps.newHashMap();
            modelInfo.put("num", i);
            if (StringUtil.isNotBlank(areaInfo.getInspectionReportPicPath())) {
                modelInfo.put("picPath", Pictures.ofUrl(areaInfo.getInspectionReportPicPath(), PictureType.suggestFileType(areaInfo.getInspectionReportPicPath()))
                        .size(385, 200).create());
            }

            modelInfo.put("xjqy", vo.getPvAreaName());
            //暂未接入巡检app
            modelInfo.put("xjhxsc", "");
            byte[] decodedBytes = Base64.getDecoder().decode(areaInfo.getCoordinates()); // 对base64编码后的字节数组进行解码
            JSONObject jsonObject = JSON.parseObject(new String(decodedBytes));
            JSONObject geometry = (JSONObject) jsonObject.get("geometry");
            JSONArray coordinates = (JSONArray) geometry.get("coordinates");
            JSONArray coordinatesOne = (JSONArray) coordinates.get(0);
            List<LatLng> pointList = new ArrayList<>();
            coordinatesOne.forEach(e -> {
                JSONArray one = (JSONArray) e;
                pointList.add(new LatLng(Double.valueOf(String.valueOf(one.get(1))), Double.valueOf(String.valueOf(one.get(0)))));
            });
            modelInfo.put("fgmj", (int) Math.round(CalculateArea.calculateArea(pointList)) + "㎡");
            int zjgl = 210;
            try {
                if (Func.isNotEmpty(stationManagement)) {
                    zjgl = Integer.parseInt(stationManagement.getComponentManufacturerPower().replace("W", "").split(StringPool.SLASH)[1]);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            modelInfo.put("sczjrl", Math.round(sczjsl * zjgl / 1000) + "MW");
            modelInfo.put("fzd", "0W/㎡");
            modelInfo.put("sczjsl", sczjsl + "块");
            modelInfo.put("sczcsl", sczcsl + "串");


            int num1 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103019") || e.getDefectDescription().equals("103022")).collect(Collectors.toList()).size();
            int num2 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103020")).collect(Collectors.toList()).size();
            int num3 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103021")).collect(Collectors.toList()).size();
            int num4 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103018")).collect(Collectors.toList()).size();
            int num5 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103023")).collect(Collectors.toList()).size();
            int num6 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103024") || e.getDefectDescription().equals("103025")).collect(Collectors.toList()).size();

            int num7 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103019") || e.getDefectDescription().equals("103020")
                    || e.getDefectDescription().equals("103021") || e.getDefectDescription().equals("103018") || e.getDefectDescription().equals("103017")).map(DeviceWithParentVO::getParentDeviceId).distinct().collect(Collectors.toList()).size();
            int num8 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103023")).map(DeviceWithParentVO::getParentDeviceId).distinct().collect(Collectors.toList()).size();
            int num9 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103024") || e.getDefectDescription().equals("103025")).map(DeviceWithParentVO::getParentDeviceId).distinct().collect(Collectors.toList()).size();
            int num10 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103017")).collect(Collectors.toList()).size();
            int num11 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103017")).map(DeviceWithParentVO::getParentDeviceId).distinct().collect(Collectors.toList()).size();
            int num12 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103022")).collect(Collectors.toList()).size();
            int num13 = oneInfos.stream().filter(e -> e.getDefectDescription().equals("103022")).map(DeviceWithParentVO::getParentDeviceId).distinct().collect(Collectors.toList()).size();

            modelInfo.put("num1", num1);
            modelInfo.put("num2", num2);
            modelInfo.put("num3", num3);
            modelInfo.put("num4", num4);
            modelInfo.put("num5", num5);
            modelInfo.put("num6", num6);
            modelInfo.put("num7", num7);
            modelInfo.put("num8", num8);
            modelInfo.put("num9", num9);
            modelInfo.put("num10", num10);
            modelInfo.put("num11", num11);
            modelInfo.put("num12", num12);
            modelInfo.put("num13", num13);
            sum1 = sum1 + num1;
            sum2 = sum2 + num2;
            sum3 = sum3 + num3;
            sum4 = sum4 + num4;
            sum5 = sum5 + num5;
            sum6 = sum6 + num6;
            sum7 = sum7 + num7;
            sum8 = sum8 + num8;
            sum9 = sum9 + num9;
            sum10 = sum10 + num10;
            sum11 = sum11 + num11;
            sum12 = sum12 + num12;
            sum13 = sum13 + num13;
            modelInfo.put("percent1", numberFormat.format((float) num1 / (float) sczjsl * 100));
            modelInfo.put("percent2", numberFormat.format((float) num2 / (float) sczjsl * 100));
            modelInfo.put("percent3", numberFormat.format((float) num3 / (float) sczjsl * 100));
            modelInfo.put("percent4", numberFormat.format((float) num4 / (float) sczjsl * 100));
            modelInfo.put("percent5", numberFormat.format((float) num5 / (float) sczjsl * 100));
            modelInfo.put("percent6", numberFormat.format((float) num6 / (float) sczjsl * 100));
            modelInfo.put("percent7", numberFormat.format((float) num7 / (float) sczcsl * 100));
            modelInfo.put("percent8", numberFormat.format((float) num8 / (float) sczcsl * 100));
            modelInfo.put("percent9", numberFormat.format((float) num9 / (float) sczcsl * 100));
            modelInfo.put("percent10", numberFormat.format((float) num10 / (float) sczjsl * 100));
            modelInfo.put("percent11", numberFormat.format((float) num11 / (float) sczcsl * 100));
            modelInfo.put("percent12", numberFormat.format((float) num12 / (float) sczjsl * 100));
            modelInfo.put("percent13", numberFormat.format((float) num13 / (float) sczcsl * 100));
            resultList.add(modelInfo);

            // 表头
            RowRenderData header = Rows.of("序号", "故障位置", "组串编号", "缺陷类型", "缺陷等级").center().bgColor("D9D9D9")
                    .textFontFamily("黑体").textFontSize(10).textBold().create();

            Tables.TableBuilder tableBuilder = Tables.ofA4MediumWidth();
            tableBuilder.addRow(header);


            List<Map<String, Object>> areaDefectList = new ArrayList<Map<String, Object>>();
            for (int j = 0; j < oneInfos.size(); j++) {

                DeviceWithParentVO oneInfo = oneInfos.get(j);

                Map<String, Object> modelInfo1 = Maps.newHashMap();
                modelInfo1.put("xh", String.valueOf(j + 1));
                modelInfo1.put("gzwz", oneInfo.getDeviceName());
                modelInfo1.put("zcbh", oneInfo.getParentDeviceName());
                modelInfo1.put("gzyy", CommonUtil.transDefectCode(oneInfo.getDefectDescription(), PV));
                modelInfo1.put("qxdj", DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), oneInfo.getDefectLevel()));
                modelInfo1.put("gzwd", oneInfo.getTemperature() + "℃");
                if (StringUtil.isNotBlank(oneInfo.getBigFilePath())) {
                    modelInfo1.put("picPath1", Pictures.ofUrl(oneInfo.getBigFilePath(), PictureType.suggestFileType(oneInfo.getBigFilePath()))
                            .size(385, 200).create());
                }
                if (StringUtil.isNotBlank(oneInfo.getLightFilePath())) {
                    modelInfo1.put("picPath2", Pictures.ofUrl(oneInfo.getLightFilePath(), PictureType.suggestFileType(oneInfo.getLightFilePath()))
                            .size(385, 200).create());
                }

                areaDefectList.add(modelInfo1);

                //表体内容
                RowRenderData row = Rows.of(String.valueOf(j + 1),
                        oneInfo.getDeviceName(),
                        oneInfo.getParentDeviceName(),
                        CommonUtil.transDefectCode(oneInfo.getDefectDescription(), PV),
                        DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), oneInfo.getDefectLevel())
                ).center().create();
                tableBuilder.addRow(row);
            }

            modelInfo.put("areaDefectList", areaDefectList);

            TableRenderData tableRenderData = tableBuilder.create();
            modelInfo.put("renderData", tableRenderData);

            zjDefectList.add(modelInfo);

            i++;
        }
        //机型规格
        dataMap.put("resultList", resultList);

        dataMap.put("num1", sum1);
        dataMap.put("num2", sum2);
        dataMap.put("num3", sum3);
        dataMap.put("num4", sum4);
        dataMap.put("num5", sum5);
        dataMap.put("num6", sum6);
        dataMap.put("num7", sum7);
        dataMap.put("num8", sum8);
        dataMap.put("num9", sum9);
        dataMap.put("num10", sum10);
        dataMap.put("num11", sum11);
        dataMap.put("num12", sum12);
        dataMap.put("num13", sum13);
        dataMap.put("percent1", numberFormat.format((float) sum1 / (float) componentsNum * 100));
        dataMap.put("percent2", numberFormat.format((float) sum2 / (float) componentsNum * 100));
        dataMap.put("percent3", numberFormat.format((float) sum3 / (float) componentsNum * 100));
        dataMap.put("percent4", numberFormat.format((float) sum4 / (float) componentsNum * 100));
        dataMap.put("percent5", numberFormat.format((float) sum5 / (float) componentsNum * 100));
        dataMap.put("percent6", numberFormat.format((float) sum6 / (float) componentsNum * 100));
        dataMap.put("percent7", numberFormat.format((float) sum7 / (float) stringNum * 100));
        dataMap.put("percent8", numberFormat.format((float) sum8 / (float) stringNum * 100));
        dataMap.put("percent9", numberFormat.format((float) sum9 / (float) stringNum * 100));
        dataMap.put("percent10", numberFormat.format((float) sum10 / (float) componentsNum * 100));
        dataMap.put("percent11", numberFormat.format((float) sum11 / (float) stringNum * 100));
        dataMap.put("percent12", numberFormat.format((float) sum12 / (float) componentsNum * 100));
        dataMap.put("percent13", numberFormat.format((float) sum13 / (float) stringNum * 100));

        //InspectionTask inspectionTask = inspectionTaskService.getById(task.getInspectionTaskId());
        InspectionTask inspectionTask = inspectionTaskMapper.selectById(task.getInspectionTaskId());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        if(StrUtil.isNotEmpty(inspectionTask.getStartDate())) {
        	dataMap.put("kssj", simpleDateFormat.format(DateUtil.parse(inspectionTask.getStartDate(), DateUtil.PATTERN_DATETIME)));
        }
        if(StrUtil.isNotEmpty(inspectionTask.getEndDate())) {
        	dataMap.put("jssj", simpleDateFormat.format(DateUtil.parse(inspectionTask.getEndDate(), DateUtil.PATTERN_DATETIME)));
        }
        //巡检区域图
        R<AllcoreFileVO> rstR = ossClient.getFileDetail(inspectionTask.getInspectionAreaGuid());
        if (rstR.isSuccess()) {
            dataMap.put("picPath", Pictures.ofUrl(rstR.getData().getStaticPath(), PictureType.suggestFileType(rstR.getData().getStaticPath()))
                    .size(385, 200).create());
        }

        defectComponentsNum = sum1 + sum2 + sum3 + sum4 + sum5 + sum6 + sum10;
        defectStringNum = sum7 + sum8 + sum9;
        // 缺陷组件数量
        dataMap.put("zjqxzs", defectComponentsNum);
        // 故障组串数量 有缺陷的组件表示该父级组串也有缺陷
        dataMap.put("gzzcsl", defectStringNum);
        // 故障比例
        if (0 == componentsNum) {
            dataMap.put("pvgzbl", 0.00 + "");
        } else {
            dataMap.put("pvgzbl", numberFormat.format((float) defectComponentsNum / (float) componentsNum * 100));
        }
        // 组串故障比例
        if (0 == stringNum) {
            dataMap.put("gzzcbl", 0.00 + "");
        } else {
            dataMap.put("gzzcbl", numberFormat.format((float) defectStringNum / (float) stringNum * 100));
        }


        dataMap.put("zjDefectList", zjDefectList);
    }


    private void fanExtraDataNew(List<String> deviceIdList, Map<String, Object> dataMap, RecognitionTask task, StationManagement stationManagement, List<DeviceWithParentVO> deviceDetails) {

        //某个缺陷类型的等级 取最严重类型

        List<DeviceWithParentVO> tagInfos = inspectionPictureTaggingMapper.fanExportTagInfos(task.getId(), deviceIdList);

        //补充红外图路径和可将光路径
        List<String> fileGuidList =
                tagInfos.stream().map(DeviceWithParentVO::getBigFileGuid).distinct().collect(Collectors.toList());
        fileGuidList.addAll(tagInfos.stream().map(DeviceWithParentVO::getFileGuid).distinct().collect(Collectors.toList()));
        fileGuidList.addAll(tagInfos.stream().map(DeviceWithParentVO::getPicFileGuid).distinct().collect(Collectors.toList()));
        R<List<AllcoreFileVO>> listFileData = ossClient.getFilesDetail(fileGuidList);
        if (listFileData.isSuccess()) {
            for (AllcoreFileVO fileVO : listFileData.getData()) {
                for (DeviceWithParentVO vo : tagInfos) {
                    if (StringUtil.isNotBlank(vo.getBigFileGuid()) && vo.getBigFileGuid().equals(fileVO.getFileGuid())) {
                        vo.setBigFilePath(fileVO.getStaticPath());
                    }
                    if (StringUtil.isNotBlank(vo.getFileGuid()) && vo.getFileGuid().equals(fileVO.getFileGuid())) {
                        vo.setFilePath(fileVO.getStaticPath());
                    }
                    if (StringUtil.isNotBlank(vo.getPicFileGuid()) && vo.getPicFileGuid().equals(fileVO.getFileGuid())) {
                        //文件名称 可解析出 大类（叶片几-背风面、塔筒-机舱）信息
                        vo.setFileName(fileVO.getOriginalName());
                        vo.setPartOneName(getPartOneName(fileVO.getOriginalName()));
                        vo.setPartTwoName(getPartTwoName(fileVO.getOriginalName()));
                    }
                }
            }
        }

        Map<String, List<DeviceWithParentVO>> map = tagInfos.stream().collect(Collectors.groupingBy(DeviceWithParentVO::getDeviceId));


        List<Map<String, Object>> deviceList = new ArrayList<>();


        int qxsl1 = 0, qxsl2 = 0, qxsl3 = 0, qxsl4 = 0, qxsl5 = 0, qxsl6 = 0, qxsl7 = 0, qxsl8 = 0, qxsl9 = 0, qxsl10 = 0, qxsl11 = 0, qxsl12 = 0, qxsl13 = 0, qxsl14 = 0;
        int qxsl15 = 0, qxsl16 = 0, qxsl17 = 0, qxsl18 = 0, qxsl19 = 0, qxsl20 = 0, qxsl21 = 0, qxsl22 = 0, qxsl23 = 0, qxsl24 = 0, qxsl25 = 0, qxsl26 = 0, qxsl27 = 0, qxsl28 = 0;
        int qxsl29 = 0, qxsl30 = 0, qxsl31 = 0, qxsl32 = 0, qxsl33 = 0, qxsl34 = 0, qxsl35 = 0, qxsl36 = 0, qxsl37 = 0, qxsl38 = 0, qxsl39 = 0, qxsl40 = 0, qxsl41 = 0, qxsl42 = 0;
        int qxsl43 = 0, qxsl44 = 0;
        int qxzs = 0;
        int i = 1;
        for (DeviceWithParentVO vo : deviceDetails) {

            Map<String, Object> modelInfo = Maps.newHashMap();
            modelInfo.put("num", i);
            modelInfo.put("xjsj", dataMap.get("xjsj"));
            modelInfo.put("deviceName", vo.getDeviceName());
            modelInfo.put("manufacturer", vo.getManufacturer());
            modelInfo.put("model", vo.getModel());
            modelInfo.put("bladeManufacturer", vo.getBladeManufacturer());

            List<Map<String, Object>> deviceDefectList = new ArrayList<>();

            List<DeviceWithParentVO> oneDeviceTagList = map.get(vo.getDeviceId());
            if (CollectionUtil.isEmpty(oneDeviceTagList)) {
                continue;
            }
            qxzs = qxzs + oneDeviceTagList.size();

            int blade1Num = 0, blade1NormalNum = 0, blade1ImportantNum = 0, blade1BigNum = 0;
            int blade2Num = 0, blade2NormalNum = 0, blade2ImportantNum = 0, blade2BigNum = 0;
            int blade3Num = 0, blade3NormalNum = 0, blade3ImportantNum = 0, blade3BigNum = 0;
            int blade4Num = 0, blade4NormalNum = 0, blade4ImportantNum = 0, blade4BigNum = 0;
            String blade1NormalTypes = "", blade1ImportantTypes = "", blade1BigTypes = "";
            String blade2NormalTypes = "", blade2ImportantTypes = "", blade2BigTypes = "";
            String blade3NormalTypes = "", blade3ImportantTypes = "", blade3BigTypes = "";
            String blade4NormalTypes = "", blade4ImportantTypes = "", blade4BigTypes = "";
            for (int j = 0; j < oneDeviceTagList.size(); j++) {
                DeviceWithParentVO tag = oneDeviceTagList.get(j);
                Map<String, Object> modelInfo1 = Maps.newHashMap();
                modelInfo1.put("xh", j + 1);
                modelInfo1.put("bladeName", tag.getPartOneName());
                switch (tag.getPartOneName()) {
                    case "叶片A":
                        blade1Num++;
                        break;
                    case "叶片B":
                        blade2Num++;
                        break;
                    case "叶片C":
                        blade3Num++;
                        break;
                    default:
                        blade4Num++;
                        break;
                }
                modelInfo1.put("model", vo.getModel());
                modelInfo1.put("bladePosition", tag.getPartTwoName());

                modelInfo1.put("defectDescriptionZh", CommonUtil.transDefectCode(tag.getDefectDescription(), FAN));

                switch (tag.getDefectDescription()) {
                    case "102001":
                        //mianqi	102001	表面面漆脱落/破损
                        qxsl1++;
                        break;
                    case "102002":
                        //jiaoyi-broke	102002	表面胶衣脱落/破损
                        //jiaoyi-side	102002	表面胶衣脱落/破损
                        //jiaoyi	102002	表面胶衣脱落/破损
                        qxsl2++;
                        break;
                    case "102003":
                        qxsl3++;
                        break;
                    case "102004":
                        qxsl4++;
                        break;
                    case "102005":
                        //zangwu	102005	表面灰尘、油污、昆虫尸体、胶带
                        qxsl5++;
                        break;
                    case "102006":
                        qxsl6++;
                        break;
                    case "102007":
                        qxsl7++;
                        break;
                    case "102008":
                        qxsl8++;
                        break;
                    case "102009":
//                        kailie-small	102009	非开放性小尺度前后缘开裂
                        qxsl9++;
                        break;
                    case "102010":
                        qxsl10++;
                        break;
                    case "102011":
                        qxsl11++;
                        break;
                    case "102012":
//                        jiaoyi-severe	102012	纤维层表面破损
                        qxsl12++;
                        break;
                    case "102013":
                        qxsl13++;
                        break;
                    case "102014":
                        qxsl14++;
                        break;
                    case "102015":
                        qxsl15++;
                        break;
                    case "102016":
                        qxsl16++;
                        break;
                    case "102017":
                        qxsl17++;
                        break;
                    case "102018":
                        qxsl18++;
                        break;
                    case "102019":
                        qxsl19++;
                        break;
                    case "102020":
                        qxsl20++;
                        break;
                    case "102021":
                        qxsl21++;
                        break;
                    case "102022":
                        qxsl22++;
                        break;
                    case "102023":
                        qxsl23++;
                        break;
                    case "102024":
                        qxsl24++;
                        break;
                    case "102025":
                        qxsl25++;
                        break;
                    case "102026":
                        qxsl26++;
                        break;
                    case "102027":
                        qxsl27++;
                        break;
                    case "102028":
                        qxsl28++;
                        break;
                    case "102029":
                        qxsl29++;
                        break;
                    case "102030":
                        qxsl30++;
                        break;
                    case "102031":
                        qxsl31++;
                        break;
                    case "102032":
                        qxsl32++;
                        break;
                    case "102033":
//                        leiji	102033	雷击
                        qxsl33++;
                        break;
                    case "102034":
//                        gubao	102034	鼓包
                        qxsl34++;
                        break;
                    case "102035":
//                        shayan	102035	砂眼
                        qxsl35++;
                        break;
                    case "102036":
//                        shayan-big	102036	砂眼（严重）
                        qxsl36++;
                        break;
                    case "102037":
//                        xiushi	102037	锈蚀
                        qxsl37++;
                        break;
                    case "102038":
//                        kailie	102038	开裂
                        qxsl38++;
                        break;
                    case "102039":
//                        huahen	102039	划痕
                        qxsl39++;
                        break;
                    case "102040":
                        qxsl40++;
                        break;
                    case "102041":
//                        油污
                        qxsl41++;
                        break;
                    case "102042":
                        qxsl42++;
                        break;
                    case "102043":
//                        涂层脱落
                        qxsl43++;
                        break;
                    case "102044":
                        qxsl44++;
                        break;
                    default:
                        break;
                }
                modelInfo1.put("defectLevelZh", getFanDefectLevel(tag.getDefectDescription()));
                switch (String.valueOf(modelInfo1.get("defectLevelZh"))) {
                    case "重大":
                        switch (tag.getPartOneName()) {
                            case "叶片A":
                                blade1BigNum++;
                                blade1BigTypes = blade1BigTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            case "叶片B":
                                blade2BigNum++;
                                blade2BigTypes = blade2BigTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            case "叶片C":
                                blade3BigNum++;
                                blade3BigTypes = blade3BigTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            default:
                                blade4BigNum++;
                                blade4BigTypes = blade4BigTypes + modelInfo1.get("defectDescriptionZh") + StringPool.NEWLINE;
                                break;
                        }
                        break;
                    case "重要":
                        switch (tag.getPartOneName()) {
                            case "叶片A":
                                blade1ImportantNum++;
                                blade1ImportantTypes = blade1ImportantTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            case "叶片B":
                                blade2ImportantNum++;
                                blade2ImportantTypes = blade2ImportantTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            case "叶片C":
                                blade3ImportantNum++;
                                blade3ImportantTypes = blade3ImportantTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            default:
                                blade4ImportantNum++;
                                blade4ImportantTypes = blade4ImportantTypes + modelInfo1.get("defectDescriptionZh") + StringPool.NEWLINE;
                                break;
                        }
                        break;
                    default:
                        switch (tag.getPartOneName()) {
                            case "叶片A":
                                blade1NormalNum++;
                                blade1NormalTypes = blade1NormalTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            case "叶片B":
                                blade2NormalNum++;
                                blade2NormalTypes = blade2NormalTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            case "叶片C":
                                blade3NormalNum++;
                                blade3NormalTypes = blade3NormalTypes + modelInfo1.get("defectDescriptionZh") + StringPool.COMMA + tag.getPartTwoName() + StringPool.SEMICOLON + StringPool.NEWLINE;
                                break;
                            default:
                                blade4NormalNum++;
                                blade4NormalTypes = blade4NormalTypes + modelInfo1.get("defectDescriptionZh") + StringPool.NEWLINE;
                                break;
                        }
                        break;
                }
                modelInfo1.put("defectHandle", getHandleByLevel(String.valueOf(modelInfo1.get("defectLevelZh"))));
                if (StringUtil.isNotBlank(tag.getFilePath())) {
                    modelInfo1.put("picPath1", Pictures.ofUrl(tag.getFilePath(), PictureType.suggestFileType(tag.getFilePath()))
                            .size(385, 200).create());
                }
                if (StringUtil.isNotBlank(tag.getBigFilePath())) {
                    modelInfo1.put("picPath2", Pictures.ofUrl(tag.getBigFilePath(), PictureType.suggestFileType(tag.getBigFilePath()))
                            .size(385, 200).create());
                }
                deviceDefectList.add(modelInfo1);
            }
            modelInfo.put("deviceDefectList", deviceDefectList);

            modelInfo.put("blade1Name", "叶片A");
            modelInfo.put("blade1Num", blade1Num);
            modelInfo.put("blade1NormalNum", blade1NormalNum);
            modelInfo.put("blade1NormalTypes", blade1NormalTypes);
            modelInfo.put("blade1ImportantNum", blade1ImportantNum);
            modelInfo.put("blade1ImportantTypes", blade1ImportantTypes);
            modelInfo.put("blade1BigNum", blade1BigNum);
            modelInfo.put("blade1BigTypes", blade1BigTypes);

            modelInfo.put("blade2Name", "叶片B");
            modelInfo.put("blade2Num", blade2Num);
            modelInfo.put("blade2NormalNum", blade2NormalNum);
            modelInfo.put("blade2NormalTypes", blade2NormalTypes);
            modelInfo.put("blade2ImportantNum", blade2ImportantNum);
            modelInfo.put("blade2ImportantTypes", blade2ImportantTypes);
            modelInfo.put("blade2BigNum", blade2BigNum);
            modelInfo.put("blade2BigTypes", blade2BigTypes);

            modelInfo.put("blade3Name", "叶片C");
            modelInfo.put("blade3Num", blade3Num);
            modelInfo.put("blade3NormalNum", blade3NormalNum);
            modelInfo.put("blade3NormalTypes", blade3NormalTypes);
            modelInfo.put("blade3ImportantNum", blade3ImportantNum);
            modelInfo.put("blade3ImportantTypes", blade3ImportantTypes);
            modelInfo.put("blade3BigNum", blade3BigNum);
            modelInfo.put("blade3BigTypes", blade3BigTypes);

            modelInfo.put("blade4Name", "塔筒-机舱");
            modelInfo.put("blade4Num", blade4Num);
            modelInfo.put("blade4NormalNum", blade4NormalNum);
            modelInfo.put("blade4NormalTypes", blade4NormalTypes);
            modelInfo.put("blade4ImportantNum", blade4ImportantNum);
            modelInfo.put("blade4ImportantTypes", blade4ImportantTypes);
            modelInfo.put("blade4BigNum", blade4BigNum);
            modelInfo.put("blade4BigTypes", blade4BigTypes);

            deviceList.add(modelInfo);

            i++;
        }
        //机型规格
        dataMap.put("deviceList", deviceList);
        dataMap.put("qxsl1", qxsl1);
        dataMap.put("qxsl2", qxsl2);
        dataMap.put("qxsl3", qxsl3);
        dataMap.put("qxsl4", qxsl4);
        dataMap.put("qxsl5", qxsl5);
        dataMap.put("qxsl6", qxsl6);
        dataMap.put("qxsl7", qxsl7);
        dataMap.put("qxsl8", qxsl8);
        dataMap.put("qxsl9", qxsl9);
        dataMap.put("qxsl10", qxsl10);
        dataMap.put("qxsl11", qxsl11);
        dataMap.put("qxsl12", qxsl12);
        dataMap.put("qxsl13", qxsl13);
        dataMap.put("qxsl14", qxsl14);
        dataMap.put("qxsl15", qxsl15);
        dataMap.put("qxsl16", qxsl16);
        dataMap.put("qxsl17", qxsl17);
        dataMap.put("qxsl18", qxsl18);
        dataMap.put("qxsl19", qxsl19);
        dataMap.put("qxsl20", qxsl20);
        dataMap.put("qxsl21", qxsl21);
        dataMap.put("qxsl22", qxsl22);
        dataMap.put("qxsl23", qxsl23);
        dataMap.put("qxsl24", qxsl24);
        dataMap.put("qxsl25", qxsl25);
        dataMap.put("qxsl26", qxsl26);
        dataMap.put("qxsl27", qxsl27);
        dataMap.put("qxsl28", qxsl28);
        dataMap.put("qxsl29", qxsl29);
        dataMap.put("qxsl30", qxsl30);
        dataMap.put("qxsl31", qxsl31);
        dataMap.put("qxsl32", qxsl32);
        dataMap.put("qxsl33", qxsl33);
        dataMap.put("qxsl34", qxsl34);
        dataMap.put("qxsl35", qxsl35);
        dataMap.put("qxsl36", qxsl36);
        dataMap.put("qxsl37", qxsl37);
        dataMap.put("qxsl38", qxsl38);
        dataMap.put("qxsl39", qxsl39);
        dataMap.put("qxsl40", qxsl40);
        dataMap.put("qxsl41", qxsl41);
        dataMap.put("qxsl42", qxsl42);
        dataMap.put("qxsl43", qxsl43);
        dataMap.put("qxsl44", qxsl44);
        dataMap.put("qxzs", qxzs);

    }

    private String getHandleByLevel(String defectLevelZh) {
        if (defectLevelZh.equals("重大")) {
            return "紧急停机/修复、更换";
        } else if (defectLevelZh.equals("重要")) {
            return "限功率运行、故障停机/修复";
        } else {
            return "周期检查/修复";
        }
    }

    private String getFanDefectLevel(String defectDescription) {

        //22-33
        if (defectDescription.equals("102022") || defectDescription.equals("102023") || defectDescription.equals("102024")
                || defectDescription.equals("102025") || defectDescription.equals("102026") || defectDescription.equals("102027")
                || defectDescription.equals("102028") || defectDescription.equals("102029") || defectDescription.equals("102030")
                || defectDescription.equals("102031") || defectDescription.equals("102032") || defectDescription.equals("102033")
                || defectDescription.equals("102036") || defectDescription.equals("102038") || defectDescription.equals("102042")) {
            return "重大";
        } else if (defectDescription.equals("102012") || defectDescription.equals("102013") || defectDescription.equals("102014")
                || defectDescription.equals("102015") || defectDescription.equals("102016") || defectDescription.equals("102017")
                || defectDescription.equals("102018") || defectDescription.equals("102019") || defectDescription.equals("102020")
                || defectDescription.equals("102021") || defectDescription.equals("102037")) {
            return "重要";
        } else {
            return "一般";
        }
    }

    private String getPartTwoName(String originalName) {
        if (originalName.contains("前缘")) {
            return "前缘";
        } else if (originalName.contains("后缘")) {
            return "后缘";
        } else if (originalName.contains("压力面")) {
            return "压力面";
        } else if (originalName.contains("负压面")) {
            return "负压面";
        } else {
            return "机舱";
        }
    }

    private String getPartOneName(String originalName) {
        if (originalName.contains("A-前缘") || originalName.contains("A-后缘") || originalName.contains("A-压力面") || originalName.contains("A-负压面")) {
            return "叶片A";
        } else if (originalName.contains("B-前缘") || originalName.contains("B-后缘") || originalName.contains("B-压力面") || originalName.contains("B-负压面")) {
            return "叶片B";
        } else if (originalName.contains("C-前缘") || originalName.contains("C-后缘") || originalName.contains("C-压力面") || originalName.contains("C-负压面")) {
            return "叶片C";
        } else {
            return "塔筒";
        }
    }


//    public static void main(String[] args) {
////        String originalText = "Hello World!"; // 原始文本内容
////
////        byte[] encodedBytes = Base64.getEncoder().encode(originalText.getBytes()); // 将字符串转换为byte数组并进行base64编码
////        System.out.println("Base64编码结果：" + new String(encodedBytes));
//
//
//        String encodedText = "eyJ0eXBlIjoiRmVhdHVyZSIsInByb3BlcnRpZXMiOnsiaWQiOiLlt6Hmo4A25Yy6In0sImdlb21ldHJ5Ijp7InR5cGUiOiJQb2x5Z29uIiwiY29vcmRpbmF0ZXMiOltbWzExNy43ODUwOTc3NDg5MTE0LDM2LjY3NjU1Nzc1MzYzMjc2NF0sWzExNy43ODUwNzE1ODgyMTAyLDM2LjY3NjE5OTM1MzA4MDI4NV0sWzExNy43ODgwNTU3ODYzOTYyOCwzNi42NzU5NjExNjM3MTcwOF0sWzExNy43ODgwMjc5NTgzNzkzNiwzNi42NzYyOTUxMDIyMTMzNF0sWzExNy43ODUwNjcwMzkxMzUxMSwzNi42NzY1NjIyNTI4MDY0OV0sWzExNy43ODUwOTc3NDg5MTE0LDM2LjY3NjU1Nzc1MzYzMjc2NF1dXX19"; // 经过base64编码后的文本内容
//
//        byte[] decodedBytes = Base64.getDecoder().decode(encodedText); // 对base64编码后的字节数组进行解码
//        JSONObject jsonObject = JSON.parseObject(new String(decodedBytes));
//        JSONObject geometry = (JSONObject) jsonObject.get("geometry");
//        JSONArray coordinates = (JSONArray) geometry.get("coordinates");
//        JSONArray coordinatesOne = (JSONArray) coordinates.get(0);
//
//
//        List<LatLng> pointList = new ArrayList<>();
//        coordinatesOne.forEach(e->{
//            JSONArray one = (JSONArray) e;
//            pointList.add(new LatLng(Double.valueOf(String.valueOf(one.get(1))),Double.valueOf(String.valueOf(one.get(0)))));
//        });
//
//
//        System.out.println("球面多边形：" + (int) Math.round(calculateArea(pointList)));
//
//    }

    /**
     * 获取各种缺陷数量
     *
     * @param dataMap
     * @return
     */
    public void getDefectNum(Map<String, Object> dataMap, List<InspectionPictureTaggingVO> pictureTaggings) {
        // 创建一个数值格式化对象
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(2);
        // 缺陷总数
        int qxzs = pictureTaggings.size();
        // 缺陷数量
        List<Integer> list = new ArrayList<>(Collections.nCopies(32, 0));
        List<String> listLevel = new ArrayList<>(Collections.nCopies(32, ""));
        String level;
        // 保存对应缺陷的数量，显示最高缺陷等级
        for (InspectionPictureTaggingVO taggingDetailInfo : pictureTaggings) {
            for (int i = 0; i < list.size(); i++) {
                if (taggingDetailInfo.getDefectDescription().indexOf((101001 + i) + "") == 0
                        || taggingDetailInfo.getDefectDescription().indexOf((102001 + i) + "") == 0
                        || taggingDetailInfo.getDefectDescription().indexOf((103001 + i) + "") == 0
                        || taggingDetailInfo.getDefectDescription().indexOf((104001 + i) + "") == 0) {
                    list.set(i, list.get(i) + 1);
                    level = listLevel.get(i);
                    if (BizDictEnum.DEFECT_LEVEL_BIG.getName().equals(taggingDetailInfo.getDefectLevelZh())) {
                        listLevel.set(i, BizDictEnum.DEFECT_LEVEL_BIG.getName());
                    } else if (BizDictEnum.DEFECT_LEVEL_URGENT.getName().equals(taggingDetailInfo.getDefectLevelZh())) {
                        if (!BizDictEnum.DEFECT_LEVEL_BIG.getName().equals(level)) {
                            listLevel.set(i, BizDictEnum.DEFECT_LEVEL_URGENT.getName());
                        }
                    } else {
                        if (!BizDictEnum.DEFECT_LEVEL_BIG.getName().equals(level)
                                && !BizDictEnum.DEFECT_LEVEL_URGENT.getName().equals(level)) {
                            listLevel.set(i, BizDictEnum.DEFECT_LEVEL_NORMAL.getName());
                        }
                    }
                    break;
                }
            }
        }
        // 保存数据
        for (int i = 0; i < list.size(); i++) {
            // 缺陷数量
            dataMap.put("qxsl" + (1 + i), list.get(i).intValue());
            // 缺陷等级
            dataMap.put("qxdj" + (1 + i), listLevel.get(i));
            // 故障比例
            if (0 == qxzs) {
                dataMap.put("gzbl" + (1 + i), 0.00 + "");
            } else {
                dataMap.put("gzbl" + (1 + i), numberFormat.format((float) list.get(i).intValue() / (float) qxzs * 100));
            }
        }
        dataMap.put("qxzs", qxzs);
    }

    /**
     * 获取设备配置的图片信息
     *
     * @param dataMap
     * @param
     */
    private void getPicConfig(Map<String, Object> dataMap, String deviceType, List<DeviceWithParentVO> deviceDetails,
                              List<InspectionPictureVO> pictures) {
        InspectionPictureVO picture = pictures.get(0);
        int index = 1;

        List<String> deviceIds;
        List<DeviceFileVO> deviceFileVOList = null;
        // 处理台账的配置图片
        switch (deviceType) {
            case TMS_LINE:
                // 查询所有巡检线路的图片
                deviceIds = deviceDetails.stream().map(DeviceWithParentVO::getParentDeviceId).distinct()
                        .collect(Collectors.toList());
                deviceFileVOList = linePicMapper.getLineFiles(deviceIds);
                break;
            case PV:
                deviceIds = deviceDetails.stream().map(DeviceWithParentVO::getDeviceId).collect(Collectors.toList());
                deviceFileVOList = pvAreaPicMapper.getPvFiles(deviceIds);
                break;
            default:
                break;
        }

        List<String> fileGuids = deviceFileVOList.stream().map(DeviceFileVO::getFileGuid).collect(Collectors.toList());
        R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
        if (rstR.isSuccess()) {
            deviceFileVOList.forEach(e -> {
                rstR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setFilePath(r.getStaticPath());
                    }
                });
            });
        }

        Map<String, List<DeviceFileVO>> map =
                deviceFileVOList.stream().collect(Collectors.groupingBy(DeviceFileVO::getFileType));
        handleDeviceFile(map, index, dataMap);

        if (deviceType.equals(FAN) || deviceType.equals(TMS_LINE)) {
            // 无人机自主飞行图片，取第一张图
            dataMap.put("wrjzzfxpic",
                    Pictures.ofUrl(picture.getFilePath(), PictureType.suggestFileType(picture.getFilePath())).size(591, 393)
                            .create());
            if (StringUtil.equals(deviceType, FAN)) {
                index = 1;
            }
            dataMap.put("wrjzzfxpicdesc", StrUtil.format("图3-{} 无人机自主飞行巡检", index));
        }
    }

    private void handleDeviceFile(Map<String, List<DeviceFileVO>> map, int index, Map<String, Object> dataMap) {
        for (Map.Entry<String, List<DeviceFileVO>> entry : map.entrySet()) {

            String format = "";
            switch (entry.getKey()) {
                case "1":
                    format = "图3-{} {}激光点云三维{}";
                    break;
                case "2":
                    format = "图3-{} {}航迹航线{}";
                    break;
                case "3":
                    format = "图3-{} {}区域可见光全景{}";
                    break;
                case "4":
                    format = "图3-{} {}区域红外全景{}";
                    break;
                case "5":
                    format = "图3-{} {}场站编码图{}";
                    break;
                default:
                    break;
            }
            List<Map<String, Object>> imageMapList = new ArrayList<>();
            Map<String, Object> imageMap;
            for (int i = 0; i < entry.getValue().size(); i++) {
                DeviceFileVO deviceFileVO = entry.getValue().get(i);
                imageMap = new HashMap<>(3);
                imageMap.put("image",
                        Pictures.ofUrl(deviceFileVO.getFilePath(), PictureType.suggestFileType(deviceFileVO.getFilePath()))
                                .size(591, 393).create());
                imageMap.put("description", StrUtil.format(format, index, deviceFileVO.getDeviceName(), i + 1));
                imageMapList.add(imageMap);
                index++;
            }
            switch (entry.getKey()) {
                case "1":
                    dataMap.put("jgdypiclist", imageMapList);
                    break;
                case "2":
                    dataMap.put("hjhxghpiclist", imageMapList);
                    break;
                case "3":
                    dataMap.put("kjgpiclist", imageMapList);
                    break;
                case "4":
                    dataMap.put("hwpiclist", imageMapList);
                    break;
                case "5":
                    dataMap.put("czbmpiclist", imageMapList);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 图片画框,保存画框的图片路径
     *
     * @param pictureDetailList
     * @param imgPath
     * @return
     */
    public void drawPicture(List<InspectionPictureVO> pictureDetailList, String imgPath) {
        Font font = new Font("宋体", Font.PLAIN, 18);
        try {
            // 获取后缀
            String[] img;
            // 生成图片
            File fileImg;
            BufferedImage image;
            Graphics g1;
            Graphics2D g;
            // 默认字体颜色
            String color = "000000";
            // 输出图片的地址
            FileOutputStream fileImgOut;
            int i = 0;
            for (InspectionPictureVO pictureDetail : pictureDetailList) {
                // 获取后缀
                img = pictureDetail.getFilePath().split("\\.");
                pictureDetail.setOriginalName(pictureDetail.getOriginalName().replace("/", "_").replace("\\", "_"));
                // 生成图片
                fileImg = new File(imgPath + File.separator + "_" + (++i) + "_" + pictureDetail.getOriginalName());
                if (!fileImg.exists()) {
                    fileImg.createNewFile();
                }
                // !!!!!!!!!!!!!!!!!!!!!!!!!!!需要图片的存储地址
                image = ImageIO.read(new URL(pictureDetail.getFilePath()));
                g1 = image.getGraphics();
                g = (Graphics2D) g1;
                g.setStroke(new BasicStroke(5.0f));
                g.setFont(font);
                int defectNum = 0;
                for (InspectionPictureTaggingVO defectTaggingDetail : pictureDetail.getInspectionPictureTaggings()) {
                    defectNum++;
                    // 画笔颜色
                    if (BizDictEnum.COMMON_YES.getCode().equals(defectTaggingDetail.getIsAlgorithm())) {
                        g.setColor(Color.yellow);
                    } else {
                        g.setColor(Color.red);
                    }
                    try {
                        if (BizDictEnum.TAGGING_TYPE_RECTANGLE.getCode().equals(defectTaggingDetail.getTaggingType())) {
                            // 矩形框(原点x坐标，原点y坐标，矩形的长，矩形的宽)
                            g.drawRect(CommonUtil.returnInt(defectTaggingDetail.getXmin()),
                                    CommonUtil.returnInt(defectTaggingDetail.getYmin()),
                                    CommonUtil.returnInt(defectTaggingDetail.getXmax() - defectTaggingDetail.getXmin()),
                                    CommonUtil.returnInt(defectTaggingDetail.getYmax() - defectTaggingDetail.getYmin()));
                        } else {
                            // 线(原点x坐标，原点y坐标，终点x，终点y)
                            Line2D lin = new Line2D.Float(CommonUtil.returnInt(defectTaggingDetail.getXmin()),
                                    CommonUtil.returnInt(defectTaggingDetail.getYmin()),
                                    CommonUtil.returnInt(defectTaggingDetail.getXmax()),
                                    CommonUtil.returnInt(defectTaggingDetail.getYmax()));
                            g.draw(lin);
                        }
                        // 图上写字
                        g.drawString(defectNum + "、", CommonUtil.returnInt(defectTaggingDetail.getXmin()),
                                CommonUtil.returnInt(defectTaggingDetail.getYmin()) - 5);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("当前图片：" + pictureDetail.getId() + "数据异常");
                    }
                }
                // 输出图片的地址
                fileImgOut = new FileOutputStream(fileImg);
                if ("PNG".equals(img[img.length - 1].toUpperCase())) {
                    ImageIO.write(image, "png", fileImgOut);
                } else {
                    ImageIO.write(image, "jpg", fileImgOut);
                }
                pictureDetail.setFileGuid(imgPath + File.separator + "_" + (i) + "_" + pictureDetail.getOriginalName());
                if (fileImgOut != null) {
                    fileImgOut.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean oneClickReview(String inspectionPictureId, String recognitionTaskId, String recognitionTaskName,
                                  String deviceType, String inspectionTaskId) {
        // TODO 单独审核 多存数据了？
        InspectionPicture picture = new InspectionPicture();
        picture.setId(inspectionPictureId);
        picture.setPicAuditStatus(BizDictEnum.AUDIT_STATUS_PASS.getCode());

        boolean flag = inspectionPictureService.updateById(picture);

        // 查询是否还有未审核的图片 如果都已审核需要生成消缺任务
        long cnt = inspectionPictureService
                .count(new QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getPicAuditStatus, "unreviewed")
                        .eq(InspectionPicture::getRecognitionTaskId, recognitionTaskId));
        // 查询不在消缺任务中的 缺陷框 ，补充消缺任务-- 人工标注的那些
        if (cnt == 0) {
            // 查询出图片的所有缺陷

            List<InspectionPictureTagging> taggingIds;
            //过滤掉实际相同的可见光缺陷框
            //遍历 如果defect_key 在 light_defect_key中存在 则舍弃
            if (StringUtil.equals(PV, deviceType)) {
                taggingIds =
                        inspectionPictureTaggingMapper.getPvDefectTaggingIds(recognitionTaskId, StringPool.EMPTY,
                                BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
                // 过滤可见光缺陷
                this.filterLightDefect(taggingIds);
                taggingIds = this.filterRepeatDefect(taggingIds);
            } else {
                taggingIds =
                        inspectionPictureTaggingMapper.getDefectTaggingIdsNotInRemove(recognitionTaskId, StringPool.EMPTY,
                                BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
            }

            if (CollectionUtil.isNotEmpty(taggingIds)) {

                // addMore
                RemoveTaskDTO removeTask = new RemoveTaskDTO();
                removeTask.setRemoveTaskName(recognitionTaskName);
                removeTask.setInspectionTaskNo(recognitionTaskName);
                removeTask.setDeviceType(deviceType);
                removeTask.setInspectionTaskId(inspectionTaskId);
                removeTask.setInspectionPictureTaggingIds(taggingIds.stream().map(InspectionPictureTagging::getId).collect(Collectors.toList()));
                // 非光伏类型，需要设置消缺任务状态为执行中
                if(!BizDictEnum.DEVICE_TYPE_PV.getCode().equals(deviceType)){
                    removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
                }

                removeTaskService.addMoreRemoveTagByTask(removeTask);
            }

            // 所有图片都审核完成 修改任务状态为完成
            update(new LambdaUpdateWrapper<>(RecognitionTask.class)
                    .set(RecognitionTask::getRecognitionTaskStatus, BizDictEnum.RECOGNITION_STATUS_COMPLETED.getCode())
                    .eq(RecognitionTask::getId, recognitionTaskId)
            );

        }

        return flag;
        // 查询是否还有未审核的图片 如果都已审核需要生成消缺任务
        // long cnt = inspectionPictureService.count(new
        // QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getPicAuditStatus, "unreviewed")
        // .eq(InspectionPicture::getRecognitionTaskId, recognitionTaskId));
        // long taskCnt = removeTaskService.count(new QueryWrapper<RemoveTask>().lambda()
        // .eq(RemoveTask::getInspectionTaskId, inspectionTaskId));
        // //都审核 且无任务
        // if(cnt==0 && taskCnt ==0){
        // update(
        // new LambdaUpdateWrapper<RecognitionTask>()
        // .eq(RecognitionTask::getId, recognitionTaskId)
        // .set(RecognitionTask::getRecognitionTaskStatus, BizDictEnum.RECOGNITION_STATUS_COMPLETED.getCode()));
        // //更新已完成的同时 需要把工单办结
        // InspectionTask status = new InspectionTask();
        // status.setId(inspectionTaskId);
        // status.setInspectionTaskStatus(THREE);
        // inspectionTaskMapper.updateById(status);
        //
        // //查询出图片的所有缺陷
        // List<String> taggingIds = inspectionPictureTaggingMapper.getDefectTaggingIds(
        // recognitionTaskId,
        // BizDictEnum.AUDIT_STATUS_PASS.getCode(),
        // BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(),
        // BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
        // RemoveTaskDTO removeTask = new RemoveTaskDTO();
        // removeTask.setRemoveTaskName(recognitionTaskName);
        // removeTask.setInspectionTaskNo(recognitionTaskName);
        // removeTask.setDeviceType(deviceType);
        // removeTask.setInspectionTaskId(inspectionTaskId);
        // removeTask.setInspectionPictureTaggingIds(taggingIds);
        // return removeTaskService.saveRemoveTask(removeTask);
        // }else{
        // return flag;
        // }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean oneClickAudit(String recognitionTaskId, String recognitionTaskName, String deviceType,
                                 String inspectionTaskId) {
        List<InspectionPicture> list = inspectionPictureService.list(new LambdaQueryWrapper<InspectionPicture>().
                eq(InspectionPicture::getInspectionTaskId, inspectionTaskId).
                eq(InspectionPicture::getPicDefectFlg, BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode()));
        if (CollectionUtil.isNotEmpty(list)){
            throw new ServiceException("存在未标注图片！");
        }
        RecognitionTask recognitionTask = getById(recognitionTaskId);
        if (null == recognitionTask){
            throw new ServiceException("非法数据");
        }

        // 查询出图片的所有缺陷
        List<InspectionPictureTagging> taggingIds;
        //过滤掉实际相同的可见光缺陷框
        //遍历 如果defect_key 在 light_defect_key中存在 则舍弃
        if (StringUtil.equals(PV, deviceType)) {
            taggingIds =
                    inspectionPictureTaggingMapper.getPvDefectTaggingIds(recognitionTaskId, StringPool.EMPTY,
                            BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
            // 过滤可见光缺陷
            this.filterLightDefect(taggingIds);
            taggingIds = this.filterRepeatDefect(taggingIds);
        } else {
            taggingIds =
                    inspectionPictureTaggingMapper.getDefectTaggingIdsNotInRemove(recognitionTaskId, StringPool.EMPTY,
                            BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
        }

        if (CollectionUtil.isNotEmpty(taggingIds)) {

            // addMore
            RemoveTaskDTO removeTask = new RemoveTaskDTO();
            removeTask.setRemoveTaskName(recognitionTaskName);
            removeTask.setInspectionTaskNo(recognitionTaskName);
            removeTask.setDeptCode(recognitionTask.getDeptCode());
            removeTask.setCreateUser(recognitionTask.getCreateUser());
            removeTask.setCreateDept(recognitionTask.getCreateDept());
            removeTask.setDeviceType(deviceType);
            removeTask.setInspectionTaskId(inspectionTaskId);
            removeTask.setInspectionPictureTaggingIds(taggingIds.stream().map(InspectionPictureTagging::getId).collect(Collectors.toList()));
            // 非光伏类型，需要设置消缺任务状态为执行中
            if(!BizDictEnum.DEVICE_TYPE_PV.getCode().equals(deviceType)){
                removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
            }
            removeTaskService.addMoreRemoveTagByTask(removeTask);
            //新增告警记录
            addAlarmInfos(taggingIds);
        }

        // 先批量将所有pic的设置成已审核
        inspectionPictureService.update(
                new LambdaUpdateWrapper<InspectionPicture>().eq(InspectionPicture::getRecognitionTaskId, recognitionTaskId)
                        .set(InspectionPicture::getPicAuditStatus, BizDictEnum.AUDIT_STATUS_PASS.getCode()));

        // 所有图片都审核完成 修改任务状态为完成
        update(new LambdaUpdateWrapper<>(RecognitionTask.class)
                .set(RecognitionTask::getRecognitionTaskStatus, BizDictEnum.RECOGNITION_STATUS_COMPLETED.getCode())
                .eq(RecognitionTask::getId, recognitionTaskId)
        );
        return Boolean.TRUE;

    }

    @Override
    public PicGroupVO getPicGroup(String recognitionTaskId) {

        List<InspectionPicture> pics = inspectionPictureService.list(
                new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getRecognitionTaskId, recognitionTaskId)
                        .eq(InspectionPicture::getBindFlag, StringPool.YES));

        List<InspectionPictureVO> pictureVOList = BeanUtil.copy(pics, InspectionPictureVO.class);

        List<String> fileGuids = pictureVOList.stream().map(InspectionPictureVO::getFileGuid).collect(Collectors.toList());
        R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
        if (rstR.isSuccess()) {
            pictureVOList.forEach(e -> {
                rstR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setOriginalName(r.getOriginalName());
                        e.setFilePath(r.getDynamicPath());
                        e.setThumbnailFilePath(r.getDynamicThumbPath());
                    }
                });
            });
        }

        List<InspectionPictureVO> noAuditPics =
                pictureVOList.stream().filter(e -> e.getPicAuditStatus().equals(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode()))
                        .sorted(Comparator.comparing(InspectionPictureVO::getOriginalName)).collect(Collectors.toList());
        List<InspectionPictureVO> auditPics =
                pictureVOList.stream().filter(e -> e.getPicAuditStatus().equals(BizDictEnum.AUDIT_STATUS_PASS.getCode()))
                        .sorted(Comparator.comparing(InspectionPictureVO::getOriginalName)).collect(Collectors.toList());
        PicGroupVO rst = new PicGroupVO();
        rst.setNoAuditPics(noAuditPics);
        rst.setAuditPics(auditPics);
        return rst;
    }

    /**
     * （请填写方法描述）
     *
     * @param inspectionPictureId
     * @return com.allcore.main.code.defect.vo.PicDefectGroupVO
     * e.setFilePath("http://***********:8000/unsafe/"+
     * (int) Math.floor(e.getXbigmin())+"x"+(int) Math.floor(e.getYbigmin())+":"+
     * (int) Math.floor(e.getXbigmax())+"x"+(int) Math.floor(e.getYbigmax())+"/"+
     * r.getStaticPath());
     * <AUTHOR>
     * @date 2024/01/12 15:07
     */
    @Override
    public PicDefectGroupVO findByInspectionPictureId(String inspectionPictureId) {
        PicDefectGroupVO rst = new PicDefectGroupVO();

        List<InspectionPictureTagging> taggings =
                inspectionPictureTaggingService.list(new LambdaQueryWrapper<InspectionPictureTagging>()
                        .eq(InspectionPictureTagging::getInspectionPictureId, inspectionPictureId));

        List<InspectionPictureTaggingVO> taggingVOList = BeanUtil.copy(taggings, InspectionPictureTaggingVO.class);
        if (CollectionUtil.isNotEmpty(taggingVOList)) {
            String deviceType = taggingVOList.get(0).getDeviceType();
            List<String> fileGuids = new ArrayList<>();
            if (PV.equals(deviceType)) {
                fileGuids =
                        taggingVOList.stream().map(InspectionPictureTaggingVO::getBigFileGuid).collect(Collectors.toList());
                R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
                if (rstR.isSuccess()) {
                    taggingVOList.forEach(e -> {
                        rstR.getData().forEach(r -> {
                            if (e.getBigFileGuid().equals(r.getFileGuid())) {
                                e.setFilePath(r.getDynamicPath());
                            }
                        });
                    });
                }
            } else {
                fileGuids =
                        taggingVOList.stream().map(InspectionPictureTaggingVO::getFileGuid).collect(Collectors.toList());
                R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
                if (rstR.isSuccess()) {
                    taggingVOList.forEach(e -> {
                        rstR.getData().forEach(r -> {
                            if (e.getFileGuid().equals(r.getFileGuid())) {
                                e.setFilePath(r.getDynamicPath());
                            }
                        });
                    });
                }
            }

            List<InspectionPictureTaggingVO> defectTaggings =
                    taggingVOList.stream().filter(e -> e.getDefectType().equals(BizDictEnum.DEFECT_TYPE_DEFECT.getCode()))
                            .collect(Collectors.toList());
            List<InspectionPictureTaggingVO> partTaggings =
                    taggingVOList.stream().filter(e -> e.getDefectType().equals(BizDictEnum.DEFECT_TYPE_PART.getCode()))
                            .collect(Collectors.toList());
            rst.setDefectTaggings(defectTaggings);
            rst.setPartTaggings(partTaggings);
        }

        return rst;
    }

    @Override
    public Boolean changeToNormal(String inspectionPictureId) {
        InspectionPicture one = inspectionPictureService.getOne(new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getId, inspectionPictureId));
        List<InspectionPicture> list = inspectionPictureService.list(new LambdaQueryWrapper<InspectionPicture>()
                .eq(InspectionPicture::getGroupId, one.getGroupId())
                .eq(InspectionPicture::getRecognitionTaskId, one.getRecognitionTaskId()));
        // 删除该图片的tag记录 修改pic状态 最终为正常 且标记为已审核
        for (InspectionPicture picture : list){
            // 删除该图片的tag记录 修改pic状态 最终为正常 且标记为已审核
            boolean a = inspectionPictureTaggingService.remove(new LambdaQueryWrapper<InspectionPictureTagging>()
                    .eq(InspectionPictureTagging::getInspectionPictureId, picture.getId()));

            picture.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode());
            //picture.setPicAuditStatus(BizDictEnum.AUDIT_STATUS_PASS.getCode());
            inspectionPictureService.updateById(picture);
        }
        return true;
    }

    @Override
    public Boolean saveOrUpdateTag(InspectionPictureTaggingDTO dto) {
        dto.setBigFileGuid(dto.getFileGuid());
        dto.setIsAlgorithm(BizDictEnum.COMMON_NO.getCode());
        dto.setEliminateStatus(BizDictEnum.TAGGING_TYPE_NOT_PUSH.getCode());
        InspectionPictureTagging inspectionPictureTagging = BeanUtil.copy(dto, InspectionPictureTagging.class);

        InspectionPicture inspectionPicture = inspectionPictureService.getById(inspectionPictureTagging.getInspectionPictureId());
        R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(CollUtil.newArrayList(dto.getFileGuid(), inspectionPicture.getFileGuid()));
        if (!rstR.isSuccess()) {
            throw new ServiceException("缺陷图片不存在");
        }
        Map<String, AllcoreFileVO> attachFileMap = rstR.getData().stream().collect(Collectors.toMap(AllcoreFileVO::getFileGuid, c -> c));
        if (dto.getDeviceType().equals("PV")) {
            PicTagNoComponentDTO request = new PicTagNoComponentDTO();
            request.setRecognitionTaskId(inspectionPictureTagging.getRecognitionTaskId());
            request.setFilePath(attachFileMap.get(inspectionPicture.getFileGuid()).getDynamicPath());
            request.setOriginalName(attachFileMap.get(inspectionPicture.getFileGuid()).getOriginalName());
            request.setFileName(attachFileMap.get(inspectionPictureTagging.getFileGuid()).getOriginalName());
            InspectionPictureTaggingVO taggingVO = BeanUtil.copy(inspectionPictureTagging, InspectionPictureTaggingVO.class);
            taggingVO.setFileGuid(attachFileMap.get(inspectionPictureTagging.getFileGuid()).getFileGuid());
            taggingVO.setFilePath(attachFileMap.get(inspectionPictureTagging.getFileGuid()).getDynamicPath());
            taggingVO.setIsCreateReport("no");
            taggingVO.setCreateDept(AuthUtil.getDeptId());
            taggingVO.setDeptCode(AuthUtil.getDeptCode());
            taggingVO.setCreateUser(AuthUtil.getUserId());

            request.setDefectTaggings(CollUtil.newArrayList(taggingVO));

            if (!sendToAlg(request)) {
                throw new ServiceException("算法识别失败");
            }
        } else {
            try {
                // 风机、线路增加大图
                // 创建缺陷图，上传缺陷图，删除缺陷图
                // 原图路径
                String fileImagePath = createPicture(attachFileMap.get(inspectionPicture.getFileGuid()).getStaticPath());

                int xMin = dto.getXmin().intValue();
                int xMax = dto.getXmax().intValue();
                int yMin = dto.getYmin().intValue();
                int yMax = dto.getYmax().intValue();

                List cordMapList = Lists.newArrayList();
                Map<String, Integer> cordMap = Maps.newHashMap();
                cordMap.put("x", xMin);
                cordMap.put("y", yMin);
                cordMap.put("width", xMax - xMin);
                cordMap.put("height", yMax - yMin);
                cordMapList.add(cordMap);
                // 原图画框
                File paintFile = paint(fileImagePath, cordMapList, "WF");
                // 切图
                Map<String, String> map = upDefectPicture(paintFile, xMin, yMin, xMax, yMax, true, 0, 0,
                        0, 0, "WF");
                // 保存大图
                inspectionPictureTagging.setBigFileGuid(map.get("defectTagBigFile"));
                inspectionPictureTagging.setMovePosition("no");
                inspectionPictureTagging.setSystemType("default");
                inspectionPictureTagging.setAuditStatus("unreviewed");
                //初始状态改为未消缺
                inspectionPictureTagging.setEliminateStatus(BizDictEnum.TAGGING_TYPE_HAVE_TO_PUSH.getCode());
                // 保存缺陷名称
                String defectLevelName = DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), inspectionPictureTagging.getDefectLevel());
                inspectionPictureTagging.setDefectName(inspectionPictureTagging.getDefectName() + ":" + defectLevelName);


                inspectionPictureTaggingService.saveOrUpdate(inspectionPictureTagging);
            } catch (Exception e) {
                throw new ServiceException("手标失败");
            }

        }

        // 人工标注的 图片改成已标注(同组状态一致)
        return inspectionPictureService.update(new LambdaUpdateWrapper<InspectionPicture>()
                .set(InspectionPicture::getPicDefectFlg, BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode())
                .eq(InspectionPicture::getGroupId, inspectionPicture.getGroupId()));
//        InspectionPicture picture = new InspectionPicture();
//        picture.setId(dto.getInspectionPictureId());
//        picture.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
//        return inspectionPictureService.updateById(picture);
    }

    public Boolean sendToAlg(PicTagNoComponentDTO dto) {
        String group = "", batchId = dto.getRecognitionTaskId(), shapesPre = "", fileId = systemProperties.getWebUrl() + StringPool.SLASH + dto.getFilePath();
        if (CollectionUtil.isNotEmpty(dto.getDefectTaggings())) {
            group = dto.getDefectTaggings().get(0).getInspectionPictureId();
            shapesPre = dto.getDefectTaggings().get(0).getDeptCode();

            List<String> tags = new ArrayList<>();
            dto.getDefectTaggings().forEach(e -> {
                String algorithmCode =
                        DictBizCache.getKey(BizDictEnum.ALGORITHM_CODE.getCode(), e.getDefectDescription());
                String str = algorithmCode + StringPool.COMMA + (int) Math.floor(e.getXmin()) + StringPool.COMMA
                        + (int) Math.floor(e.getYmin()) + StringPool.COMMA + (int) Math.floor(e.getXmax()) + StringPool.COMMA
                        + (int) Math.floor(e.getYmax());
                tags.add(str);
            });

            AlgorithmRequestFile requestFile = new AlgorithmRequestFile();
            requestFile.setAlgorithmType("gfb");
            requestFile.setManualMode("True");
            requestFile.setLocation(sourceProperties.getShapesSystem() + StringPool.UNDERSCORE + shapesPre + "_shapes");
            requestFile.setGfbZjInfor(tags);
            List<AlgorithmRequestFileDetail> fileDetailList = new ArrayList<>(Collections.nCopies(2, null));
            String link = fileId;
            String picName = dto.getOriginalName().split("\\.")[0];
            // _T 红外
            if (StrUtil.endWith(picName, "_T")) {
                dto.setIsLight("no");
                fileDetailList.set(1, AlgorithmRequestFileDetail.builder()
                        .fileId(group)
                        .filePath(link)
                        .build());
            }else {
                dto.setIsLight("yes");
                fileDetailList.set(0, AlgorithmRequestFileDetail.builder()
                        .fileId(group)
                        .filePath(link)
                        .build());
            }
            requestFile.setFileList(fileDetailList);
            String requestStr = JSONObject.toJSONString(requestFile);
            return unAutoAlgNew(defectProperties.getUnAutoAlgPostUrl(), requestStr, dto);
//            Map<String, Object> map = Maps.newHashMap();
//            // 先放图片id
//            map.put("group", group);
//            // 原图图片全路径
//            map.put("fileId", fileId);
//            // 缺陷识别任务id
//            map.put("batch_id", batchId);
//            map.put("manual_mode", "True");
//            map.put("gfb_zj_infor", tags);
//            map.put("location", sourceProperties.getShapesSystem() + StringPool.UNDERSCORE + shapesPre + "_shapes");
//            // 进行算法调用
//            log.info(">>>>人工标注地址: {}", defectProperties.getUnAutoAlgPostUrl());
//            log.info(">>>>人工标注参数: {}", JSON.toJSONString(map));
//            // 通过上下文获取该类的实例，再调用该实例方法
//            return unAutoAlg(defectProperties.getUnAutoAlgPostUrl(), map, dto);
        }

        return true;
    }


    @Override
    public void batchDownload(String recognitionTaskIds, String downloadType, HttpServletResponse response,
                              String fileName) {
        // 当前方法生成的文件夹路径

        String path = System.getProperty("user.dir") + File.separator + fileName;
        try {
            // 全部原图片
            File pathFile = new File(path);
            if (!pathFile.exists()) {
                pathFile.mkdir();
            }
            // 所有任务
            List<RecognitionTask> list = baseMapper.selectBatchIds(Func.toStrList(recognitionTaskIds));
            for (RecognitionTask recognitionTask : list) {
                // 当前任务的所有图片
                List<InspectionPicture> pictures =
                        inspectionPictureService.list(new LambdaQueryWrapper<InspectionPicture>()
                                .eq(InspectionPicture::getRecognitionTaskId, recognitionTask.getId()));
                // 图片的所有fileGuid
                List<InspectionPictureVO> pictureVOList = BeanUtil.copy(pictures, InspectionPictureVO.class);

                List<String> fileGuids =
                        pictureVOList.stream().map(InspectionPictureVO::getFileGuid).collect(Collectors.toList());
                R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
                if (rstR.isSuccess()) {
                    pictureVOList.forEach(e -> {
                        rstR.getData().forEach(r -> {
                            if (e.getFileGuid().equals(r.getFileGuid())) {
                                e.setOriginalName(r.getOriginalName());
                                e.setFilePath(r.getStaticPath());
                            }
                        });
                    });
                }
                File taskFile = new File(path + File.separator + recognitionTask.getRecognitionTaskName());
                if (!taskFile.exists()) {
                    taskFile.mkdir();
                }
                // 防止文件名称一样被覆盖
                int i = 0;
                for (InspectionPictureVO pictureVO : pictureVOList) {
                    if (StringUtil.isBlank(pictureVO.getFilePath())) {
                        log.error("下载图片时，当前图片没有获取到图片文件信息：" + pictureVO.getId());
                        continue;
                    }
                    // 判断下载的类型
                    if (StringPool.ONE.equals(downloadType)) {
                        i++;
                    } else {
                        // 已标注
                        if (BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode().equals(pictureVO.getPicDefectFlg())) {
                            i++;
                        } else {
                            continue;
                        }
                    }
                    downloadImgAndJson(path, pictureVO, recognitionTask, i, downloadType);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成图片或算法文件
     *
     * @param path
     * @param
     * @param
     * @param i
     * @param downloadType
     */
    public void downloadImgAndJson(String path, InspectionPictureVO pictureVO, RecognitionTask recognitionTask, int i,
                                   String downloadType) {
        Font font = new Font("宋体", Font.PLAIN, 18);
        FileOutputStream out;
        // 判断是原图1还是标注图片
        // 后缀
        String type = "";
        if (!StringUtil.isBlank(pictureVO.getOriginalName())) {
            pictureVO.setOriginalName(pictureVO.getOriginalName().replace("/", "_").replace("\\", "_"));
            type = pictureVO.getOriginalName().substring(pictureVO.getOriginalName().lastIndexOf("."));
        }
        String fileImgPath = path + File.separator + recognitionTask.getRecognitionTaskName() + File.separator
                + pictureVO.getOriginalName();
        // 原图
        if (StringPool.ONE.equals(downloadType)) {
            /**
             * 创建图片
             */
            createFile(fileImgPath, pictureVO);
        } else {
            // 已标注图片和算法文件
            // 判断是否是标注图片
            if (!BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode().equals(pictureVO.getPicDefectFlg())) {
                return;
            }
            /**
             * 创建图片
             */
            createFile(fileImgPath, pictureVO);
            // 获取所有缺陷
            List<InspectionPictureTagging> pictureTaggings =
                    inspectionPictureTaggingService.list(new QueryWrapper<InspectionPictureTagging>().lambda()
                            .eq(InspectionPictureTagging::getInspectionPictureId, pictureVO.getId()));
            if (pictureTaggings == null || pictureTaggings.size() == 0) {
                return;
            }
            // 图上画框
            BufferedImage image;
            try {
                image = ImageIO.read(new File(fileImgPath));
                Graphics g1 = image.getGraphics();
                Graphics2D g = (Graphics2D) g1;
                g.setStroke(new BasicStroke(5.0f));
                g.setFont(font);
                int defectNum = 0;
                String png = "PNG";
                for (InspectionPictureTagging pictureTagging : pictureTaggings) {
                    defectNum++;
                    // 设置画笔颜色
                    g.setColor(Color.red);
                    if (BizDictEnum.DEFECT_TYPE_PART.getCode().equals((pictureTagging.getDefectType()))) {
                        g.setColor(Color.blue);
                    }
                    if (BizDictEnum.COMMON_YES.getCode().equals(pictureTagging.getIsAlgorithm())) {
                        g.setColor(Color.yellow);
                    }
                    try {
                        if (BizDictEnum.TAGGING_TYPE_RECTANGLE.getCode().equals(pictureTagging.getTaggingType())) {
                            // 矩形框(原点x坐标，原点y坐标，矩形的长，矩形的宽)
                            g.drawRect(CommonUtil.returnInt(pictureTagging.getXmin()),
                                    CommonUtil.returnInt(pictureTagging.getYmin()),
                                    CommonUtil.returnInt(pictureTagging.getXmax() - pictureTagging.getXmin()),
                                    CommonUtil.returnInt(pictureTagging.getYmax() - pictureTagging.getYmin()));
                        } else {
                            // 线(原点x坐标，原点y坐标，终点x，终点y)
                            Line2D lin = new Line2D.Float(CommonUtil.returnInt(pictureTagging.getXmin()),
                                    CommonUtil.returnInt(pictureTagging.getYmin()),
                                    CommonUtil.returnInt(pictureTagging.getXmax()),
                                    CommonUtil.returnInt(pictureTagging.getYmax()));
                            g.draw(lin);
                        }
                        // 缺陷查询导出场站缺陷图片的时候不需要缺陷描述
                        if (!"full_defect".equals(downloadType)) {
                            g.drawString(defectNum + "、" + pictureTagging.getDefectName(),
                                    CommonUtil.returnInt(pictureTagging.getXmin()),
                                    CommonUtil.returnInt(pictureTagging.getYmin()) - 5);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                // 输出图片的地址
                out = new FileOutputStream(fileImgPath);
                if (png.equals(type.toUpperCase())) {
                    ImageIO.write(image, "png", out);
                } else {
                    ImageIO.write(image, "jpg", out);
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 创建图片
     *
     * @param fileImgPath
     * @param
     */
    public void createFile(String fileImgPath, InspectionPictureVO pictureVO) {
        FileOutputStream out = null;
        InputStream in = null;
        try {
            URL url = new URL(pictureVO.getFilePath());
            HttpURLConnection urlCon = (HttpURLConnection) url.openConnection();
            if (urlCon.getResponseCode() != ResultCode.SUCCESS.getCode()) {
                log.error("文件不存在,id=:" + pictureVO.getId() + "__" + pictureVO.getFilePath());
                return;
            }
            File fileImg = new File(fileImgPath);
            // 创建文件
            if (!fileImg.exists()) {
                fileImg.createNewFile();
            }
            // -----------------------------------------------------图片
            in = url.openStream();
            // 写入相应的文件
            out = new FileOutputStream(fileImg);
            // 读取数据
            // 一次性取多少字节
            byte[] bytes = new byte[2048];
            // 接受读取的内容(n就代表的相关数据，只不过是数字的形式)
            int n = -1;
            // 循环取出数据
            while ((n = in.read(bytes, 0, bytes.length)) != -1) {
                // 写入相关文件
                out.write(bytes, 0, n);
                // 清除缓存向文件写入数据
                out.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭流
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public PicGroupVO getPicGroupDetail(String recognitionTaskId) {

        List<InspectionPicture> pics = inspectionPictureService.list(
                new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getRecognitionTaskId, recognitionTaskId)
                        .eq(InspectionPicture::getBindFlag, StringPool.YES));

        List<InspectionPictureVO> pictureVOList = BeanUtil.copy(pics, InspectionPictureVO.class);

        List<String> fileGuids = pictureVOList.stream().map(InspectionPictureVO::getFileGuid).collect(Collectors.toList());
        R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
        if (rstR.isSuccess()) {
            pictureVOList.forEach(e -> {
                rstR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setOriginalName(r.getOriginalName());
                        e.setFilePath(r.getDynamicPath());
                        e.setThumbnailFilePath(r.getDynamicThumbPath());
                    }
                });
            });
        }

        List<InspectionPictureVO> unlabeledPics =
                pictureVOList.stream().filter(e -> e.getPicDefectFlg().equals(BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode()))
                        .sorted(Comparator.comparing(InspectionPictureVO::getOriginalName)).collect(Collectors.toList());
        List<InspectionPictureVO> noDefectPics =
                pictureVOList.stream().filter(e -> e.getPicDefectFlg().equals(BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode()))
                        .sorted(Comparator.comparing(InspectionPictureVO::getOriginalName)).collect(Collectors.toList());
        List<InspectionPictureVO> alreadyAnnotationPics =
                pictureVOList.stream().filter(e -> e.getPicDefectFlg().equals(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode()))
                        .sorted(Comparator.comparing(InspectionPictureVO::getOriginalName)).collect(Collectors.toList());
        PicGroupVO rst = new PicGroupVO();
        rst.setUnlabeledPics(fillPicGroup(unlabeledPics));
        rst.setNoDefectPics(fillPicGroup(noDefectPics));
        rst.setAlreadyAnnotationPics(fillPicGroup(alreadyAnnotationPics));
        return rst;
    }

    private List<InspectionPictureGroupVO> fillPicGroup(List<InspectionPictureVO> list) {
        return list.stream().filter(pic -> StringUtils.isNotBlank(pic.getGroupId()))
                .collect(Collectors.groupingBy(InspectionPictureVO::getGroupId))
                .entrySet().stream().map(m -> {
                    val groupVO = InspectionPictureGroupVO.builder().groupId(m.getKey()).build();
                    m.getValue().forEach(e -> {
                        if (StringUtils.equals(CommonUtil.getPicType(e.getOriginalName()), "_T")) {
                            groupVO.setInfrared(e);
                        } else {
                            groupVO.setVisibleLight(e);
                        }
                    });
                    return groupVO;
                }).collect(Collectors.toList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteTaggingDetailInfo(String inspectionPictureTaggingId, String inspectionPictureId) throws Exception {
        if (!StringUtil.isBlank(inspectionPictureTaggingId)) {

            // 删除缺陷详情信息
            inspectionPictureTaggingService.remove(new LambdaQueryWrapper<InspectionPictureTagging>()
                    .eq(InspectionPictureTagging::getId, inspectionPictureTaggingId));
            // 没有缺陷标记为正常
            List<InspectionPictureTagging> l =
                    inspectionPictureTaggingService.list(new QueryWrapper<InspectionPictureTagging>().lambda()
                            .eq(InspectionPictureTagging::getInspectionPictureId, inspectionPictureId));
            if (l.size() == 0) {
                InspectionPicture picture = new InspectionPicture();
                picture.setId(inspectionPictureId);
                picture.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode());
                picture.setPicAuditStatus(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode());
                inspectionPictureService.updateById(picture);
            }

            return R.success("删除成功");
        }
        return R.fail("删除失败");
    }

    @Override
    public List<String> getWorkOrderNoList(String deptCode) {
        List<String> workOrderNos = new ArrayList<>();
        List<RecognitionTask> list = this.list(new QueryWrapper<RecognitionTask>().lambda()
                .likeRight(RecognitionTask::getDeptCode, StrUtil.isBlank(deptCode) ? AuthUtil.getDeptCode() : deptCode));
        if (CollectionUtil.isNotEmpty(list)) {
            workOrderNos = list.stream().map(x -> x.getInspectionTaskNo()).collect(Collectors.toList());
        }
        return workOrderNos;
    }

    private Boolean unAutoAlgNew(String unAutoAlgPostUrl, String requestStr, PicTagNoComponentDTO dto) {
        log.info("算法识别地址：====={}，请求参数：====={}", unAutoAlgPostUrl, requestStr);
        long requestStart = System.currentTimeMillis();
        cn.hutool.http.HttpResponse response = HttpRequest.post(unAutoAlgPostUrl)
                .header("Content-Type",
                        "application/json;charset=utf-8")
                .body(requestStr).execute();
        long requestEnd = System.currentTimeMillis();
        int responseStatus = response.getStatus();
        log.info("算法识别请求返回状态：{}", responseStatus);
        log.info("算法识别请求耗时{}ms，返回内容：{}", requestEnd - requestStart, response.body());
        if (responseStatus != HttpStatus.HTTP_OK) {
            log.error("算法识别请求失败，丢弃该缺陷");
            throw new ServiceException("算法识别请求失败");
        }
        AlgorithmResult result = JSONObject.parseObject(response.body(), AlgorithmResult.class);
        if (result == null || !CommonConstant.ZERO_NUM.equals(result.getCode()) || CollUtil.isEmpty(result.getData())) {
            log.error("算法识别返回内容出错，丢弃该缺陷");
            throw new ServiceException("算法识别返回内容出错");
        }
        // 成功
        List<AlgorithmResultDefectDetail> defectList = result.getData().get(0).getDefectList();
        if (CollUtil.isEmpty(defectList)) {
            log.error("算法未识别出缺陷信息，丢弃该缺陷");
            throw new ServiceException("算法未识别出缺陷信息");
        }
        AlgorithmResultDefectDetail detail = defectList.get(0);
        String pvComponentName = detail.getZjInfo();
        if (StrUtil.isBlank(pvComponentName)) {
            log.error("算法未识别出组件信息，丢弃该缺陷");
            throw new ServiceException("算法未识别出组件信息");
        }
        try {
            List<Map<String, Integer>> cordMapList = collectCordMap(dto);
            // 原图
            String fileImagePath = createPicture(systemProperties.getWebUrl() + StringPool.SLASH + dto.getFilePath());

            File paintFile = paint(fileImagePath, cordMapList, "");

            handleAlgTagNew(dto, detail, paintFile);

            // 获取图片明细信息
            InspectionPicture inspectionPicture = inspectionPictureService
                    .getOne(new QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getId, dto.getDefectTaggings().get(0).getInspectionPictureId()));

            // 组件名称改组件id
            List<InspectionPictureTaggingVO> taggingVOList =
                    setDefectDeviceGuid(dto.getDefectTaggings(), dto.getDefectTaggings().get(0).getDeptCode(), inspectionPicture);

            List<InspectionPictureTagging> list = BeanUtil.copy(taggingVOList, InspectionPictureTagging.class);
            //补全手标信息
            for (InspectionPictureTagging tag : list) {
                tag.setIsZ(dto.getIsLight());
                tag.setMovePosition("no");
                tag.setSystemType("default");
                tag.setAuditStatus("unreviewed");
                //非光伏类型改为未消缺状态
                if (!PV.equalsIgnoreCase(tag.getDeviceType())) {
                    tag.setEliminateStatus(BizDictEnum.TAGGING_TYPE_HAVE_TO_PUSH.getCode());
                }

            }
            inspectionPictureTaggingService.saveOrUpdateBatch(list);

            inspectionPictureService.updateById(inspectionPicture);
            // 上传结束,删除带缺陷框的原图
            paintFile.delete();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }


        return true;
    }

    /**
     * （汉创算法调用）
     *
     * @param callUrl
     * @param map
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/01 16:55
     */
    @Override
    public Boolean unAutoAlg(String callUrl, Map<String, Object> map, PicTagNoComponentDTO dto) {
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(callUrl);
        String res = "";
        try {
            post.addHeader("Content-Type", "application/json; charset=utf-8");
            String jsonentity = JSON.toJSONString(map);
            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            post.setEntity(entity);
            HttpResponse response = client.execute(post);
            res = EntityUtils.toString(response.getEntity());
            log.info("============人工标注算法返回 ====================={}", res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            OwnUnAutoAlgorithmResultVO ownAlgorithmResultVO =
                    JsonUtil.convertValue(jsonObject, OwnUnAutoAlgorithmResultVO.class);
            log.info("============人工标注算法返回 解析对象====================={}", ownAlgorithmResultVO);
            if (0 == ownAlgorithmResultVO.getRetCode()
                    && CollectionUtil.isNotEmpty(ownAlgorithmResultVO.getData().getAlarms())) {
                // 识别的内容顺序 把入参集合的值更新

                List<Map<String, Integer>> cordMapList = collectCordMap(dto);
                // 原图
                String fileImagePath = createPicture(systemProperties.getWebUrl() + StringPool.SLASH + dto.getFilePath());

                File paintFile = paint(fileImagePath, cordMapList, "");

                handleAlgTag(dto, ownAlgorithmResultVO, paintFile);

                // 获取图片明细信息
                InspectionPicture inspectionPicture = inspectionPictureService
                        .getOne(new QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getId, dto.getDefectTaggings().get(0).getInspectionPictureId()));

                // 组件名称改组件id
                List<InspectionPictureTaggingVO> taggingVOList =
                        setDefectDeviceGuid(dto.getDefectTaggings(), dto.getDefectTaggings().get(0).getDeptCode(), inspectionPicture);

                List<InspectionPictureTagging> list = BeanUtil.copy(taggingVOList, InspectionPictureTagging.class).stream()
                        .peek(tag -> {
                            if (!PV.equalsIgnoreCase(tag.getDeviceType())) {
                                tag.setEliminateStatus(BizDictEnum.TAGGING_TYPE_HAVE_TO_PUSH.getCode());
                            }
                            // 返回处理后的对象
                        })
                        .collect(Collectors.toList());
                inspectionPictureTaggingService.saveOrUpdateBatch(list);

                inspectionPictureService.updateById(inspectionPicture);
                // 上传结束,删除带缺陷框的原图
                paintFile.delete();
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    private void handleAlgTagNew(PicTagNoComponentDTO dto, AlgorithmResultDefectDetail detail, File paintFile) {


        // 在原图画某个人工标注的组件框
        // 根据算法返回的组件名称 更新组件id、温度
        // 再根据算法返回的组串坐标截取大图


        InspectionPictureTaggingVO e = dto.getDefectTaggings().get(0);

        int s7 = 7;
        if (detail.getZjInfo() != null) {
            // 先放光伏组件名称
            // {"status":true,"data":{"start_time":1.699607249218532E9,"end_time":1.69960724946283E9,"width":640,"height":512,"count":2,"batch_id":"222afd0b6d3c43ac8405b86c2f921da6","algorithm_type":PV,"group":"1b3ac10c155249ceb4ec857ab455f0ee_Z","file_id":"e14eab0c10a443c08bec456a1de9e714.JPG","alarms":[[0,73,318,96,360,"hotspot",0.952,"",38.0],[1,552,264,582,309,"hotspot",0.872,"",0.0]]},"message":0}
            e.setPvComponentName(String.valueOf(detail.getZjInfo()));
        }
        int s8 = 8;
        if (detail.getTemperatures() != null) {
            e.setTemperature(String.valueOf(detail.getTemperatures()));
        }

        int s9 = 9;
        String bigTag = detail.getZcInfo();
        int[][] array = getSpecificArrays(bigTag);
        if (StringUtil.isNotBlank(bigTag)) {
            e.setXbigmin(Math.floor(array[0][0]));
            e.setXbigmax(Math.floor(array[1][0]));
            e.setYbigmin(Math.floor(array[0][1]));
            e.setYbigmax(Math.floor(array[1][1]));

        }

        int xMin = e.getXmin().intValue();
        int xMax = e.getXmax().intValue();
        int yMin = e.getYmin().intValue();
        int yMax = e.getYmax().intValue();

        int xBigMin = null != e.getXbigmin() ? e.getXbigmin().intValue() : 0;
        int xBigMax = null != e.getXbigmax() ? e.getXbigmax().intValue() : 0;
        int yBigMin = null != e.getYbigmin() ? e.getYbigmin().intValue() : 0;
        int yBigMax = null != e.getYbigmax() ? e.getYbigmax().intValue() : 0;

        File detectBigFile =
                new File(System.getProperty("user.dir") + File.separator + CommonUtil.generateUuid() + ".jpg");

        // 如果没拿到组串坐标 也用自动放大版裁剪
        cutBigPicture(paintFile, xMin, yMin, xMax, yMax, detectBigFile);
//        if (0 == xBigMin && 0 == yBigMin && 0 == xBigMax && 0 == yBigMax) {
//            cutBigPicture(paintFile, xMin, yMin, xMax, yMax, detectBigFile);
//        } else {
//            cutPvBigPicture(paintFile, xBigMin, yBigMin, xBigMax, yBigMax, detectBigFile);
//        }

        try (FileInputStream inBigFile = new FileInputStream(detectBigFile)) {
            MultipartFile multipartBigFile = FileUtils.getMultipartFile(inBigFile, detectBigFile.getName());
            R<AllcoreFileVO> rstBig = ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                    multipartBigFile, StringPool.NO, "");
            // 更新组串图id
            e.setBigFileGuid(rstBig.getData().getFileGuid());
        } catch (Exception ee) {
            ee.printStackTrace();
        } finally {
            try {
                detectBigFile.delete();
            } catch (Exception eee) {
                eee.printStackTrace();
            }
        }

    }

    /**
     * 将输入的字符串转换为二维数组，并返回下标为 0 和 3 的元素。
     *
     * @param input 输入的字符串，格式为 [[x1,y1],[x2,y2],...]
     * @return 一个二维数组，包含下标为 0 和 3 的元素
     * @throws IllegalArgumentException       如果输入字符串格式不正确
     * @throws ArrayIndexOutOfBoundsException 如果数组长度不足 4
     */
    public static int[][] getSpecificArrays(String input) {
        // 输入校验
        if (input == null || input.isEmpty() || !input.startsWith("[[") || !input.endsWith("]]")) {
            throw new IllegalArgumentException("输入字符串格式不正确");
        }

        // 去掉所有空格
        input = input.replaceAll("\\s", "");

        // 去掉最外层的括号
        String trimmedInput = input.substring(1, input.length() - 1);

        // 按 "],[" 分割字符串，得到每个点的字符串
        String[] pointStrings = trimmedInput.split("\\],\\[");

        // 创建一个二维数组来存储所有点
        int[][] pointsArray = new int[pointStrings.length][2];

        // 遍历每个点的字符串
        for (int i = 0; i < pointStrings.length; i++) {
            // 去掉每个点字符串的括号
            String trimmedPointString = pointStrings[i].replace("[", "").replace("]", "");

            // 按逗号分割，得到 x 和 y 的值
            String[] coordinates = trimmedPointString.split(",");
            if (coordinates.length != 2) {
                throw new IllegalArgumentException("坐标格式不正确");
            }

            try {
                int x = Integer.parseInt(coordinates[0]);
                int y = Integer.parseInt(coordinates[1]);
                pointsArray[i][0] = x;
                pointsArray[i][1] = y;
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("坐标值必须是整数");
            }
        }

        // 检查数组长度是否足够
        if (pointsArray.length < 4) {
            throw new ArrayIndexOutOfBoundsException("数组长度不足 4，无法获取下标为 3 的元素");
        }

        // 获取下标为 0 和 3 的元素
        int[] point0 = pointsArray[0]; // 下标为 0 的元素
        int[] point3 = pointsArray[2]; // 下标为 3 的元素

        // 返回结果
        return new int[][]{point0, point3};
    }

    private void handleAlgTag(PicTagNoComponentDTO dto, OwnUnAutoAlgorithmResultVO ownAlgorithmResultVO, File paintFile) {
        for (int i = 0; i < dto.getDefectTaggings().size(); i++) {

            // 在原图画某个人工标注的组件框
            // 根据算法返回的组件名称 更新组件id、温度
            // 再根据算法返回的组串坐标截取大图
            List<Object> alarms = ownAlgorithmResultVO.getData().getAlarms().get(i);

            InspectionPictureTaggingVO e = dto.getDefectTaggings().get(i);

            int s7 = 7;
            if (alarms.get(s7) != null) {
                // 先放光伏组件名称
                // {"status":true,"data":{"start_time":1.699607249218532E9,"end_time":1.69960724946283E9,"width":640,"height":512,"count":2,"batch_id":"222afd0b6d3c43ac8405b86c2f921da6","algorithm_type":PV,"group":"1b3ac10c155249ceb4ec857ab455f0ee_Z","file_id":"e14eab0c10a443c08bec456a1de9e714.JPG","alarms":[[0,73,318,96,360,"hotspot",0.952,"",38.0],[1,552,264,582,309,"hotspot",0.872,"",0.0]]},"message":0}
                e.setPvComponentName(String.valueOf(alarms.get(7)));
            }
            int s8 = 8;
            if (alarms.get(s8) != null) {
                e.setTemperature(String.valueOf(alarms.get(8)));
            }

            int s9 = 9;
            if (alarms.get(s9) != null) {
                List<Object> bigTag = (List<Object>) alarms.get(s9);
                if (CollectionUtil.isNotEmpty((bigTag))) {
                    e.setXbigmin(Math.floor(Double.parseDouble(bigTag.get(0).toString())));
                    e.setXbigmax(Math.floor(Double.parseDouble(bigTag.get(2).toString())));
                    e.setYbigmin(Math.floor(Double.parseDouble(bigTag.get(1).toString())));
                    e.setYbigmax(Math.floor(Double.parseDouble(bigTag.get(3).toString())));
                }
            }

            int xMin = e.getXmin().intValue();
            int xMax = e.getXmax().intValue();
            int yMin = e.getYmin().intValue();
            int yMax = e.getYmax().intValue();

            int xBigMin = null != e.getXbigmin() ? e.getXbigmin().intValue() : 0;
            int xBigMax = null != e.getXbigmax() ? e.getXbigmax().intValue() : 0;
            int yBigMin = null != e.getYbigmin() ? e.getYbigmin().intValue() : 0;
            int yBigMax = null != e.getYbigmax() ? e.getYbigmax().intValue() : 0;

            File detectBigFile =
                    new File(System.getProperty("user.dir") + File.separator + CommonUtil.generateUuid() + ".jpg");

            // 如果没拿到组串坐标 也用自动放大版裁剪
            if (0 == xBigMin && 0 == yBigMin && 0 == xBigMax && 0 == yBigMax) {
                cutBigPicture(paintFile, xMin, yMin, xMax, yMax, detectBigFile);
            } else {
                cutPvBigPicture(paintFile, xBigMin, yBigMin, xBigMax, yBigMax, detectBigFile);
            }

            try (FileInputStream inBigFile = new FileInputStream(detectBigFile)) {
                MultipartFile multipartBigFile = FileUtils.getMultipartFile(inBigFile, detectBigFile.getName());
                R<AllcoreFileVO> rstBig = ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                        multipartBigFile, StringPool.NO, "");
                // 更新组串图id
                e.setBigFileGuid(rstBig.getData().getFileGuid());
            } catch (Exception ee) {
                ee.printStackTrace();
            } finally {
                try {
                    detectBigFile.delete();
                } catch (Exception eee) {
                    eee.printStackTrace();
                }
            }
        }
    }

    private List<Map<String, Integer>> collectCordMap(PicTagNoComponentDTO dto) {
        List<Map<String, Integer>> cordMapList = Lists.newArrayList();

        for (int i = 0; i < dto.getDefectTaggings().size(); i++) {

            InspectionPictureTaggingVO e = dto.getDefectTaggings().get(i);
            int xMin = e.getXmin().intValue();
            int xMax = e.getXmax().intValue();
            int yMin = e.getYmin().intValue();
            int yMax = e.getYmax().intValue();
            Map<String, Integer> cordMap = Maps.newHashMap();
            cordMap.put("x", xMin);
            cordMap.put("y", yMin);
            cordMap.put("width", xMax - xMin);
            cordMap.put("height", yMax - yMin);
            cordMapList.add(cordMap);
        }
        return cordMapList;
    }

    @Override
    public void algorithmCallback(AlgorithmResult algorithmResult) {
        boolean isSuccess = CommonConstant.ZERO_NUM.equals(algorithmResult.getCode());
        AlgorithmResultDetail algorithmResultDetail = algorithmResult.getData().get(0);
        //任务ID
        String recognitionTaskGuid = algorithmToolComponentService.get(algorithmResultDetail.getTaskId());
        RecognitionTaskAlgorithm recognitionTaskAlgorithm = recognitionTaskAlgorithmService.getOne(new QueryWrapper<RecognitionTaskAlgorithm>().lambda().eq(RecognitionTaskAlgorithm::getRecognitionTaskId, recognitionTaskGuid));
        if (Func.isEmpty(recognitionTaskAlgorithm)) {
            log.error("算法识别失败，算法类型不存在 {}-{}", recognitionTaskGuid, algorithmResultDetail.getFileId());
            return;
        }
        AlgorithmManufacturers algorithm = algorithmManufacturersService.getOne(new LambdaQueryWrapper<AlgorithmManufacturers>().eq(AlgorithmManufacturers::getId, recognitionTaskAlgorithm.getAlgorithmManufacturersId()));
        if (Func.isEmpty(algorithm)) {
            log.error("算法识别失败，算法类型不存在 {}-{}", recognitionTaskGuid, algorithmResultDetail.getFileId());
            return;
        }
        //根据算法返回类型风光点判断调用方法，添加风光点过滤
        Object defectCodePrefix = redis.get(DEFECT_RECOGNITION_TASK_CODE.concat(recognitionTaskGuid));
        String algorithmType = algorithm.getAlgorithmType();
        //图片ID(inspect_picture_detail表主键)
        String picId = algorithmResultDetail.getFileId();
        // 获取图片明细信息
        InspectionPicture inspectionPicture = inspectionPictureService
                .getOne(new QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getFileGuid, picId));
        RecognitionTask recognitionTask =
                getOne(new QueryWrapper<RecognitionTask>().lambda().eq(RecognitionTask::getId, recognitionTaskGuid));
        if (isSuccess) {
            log.info("算法识别成功{}", picId);
            //算法识别成功
            if (!BizDictEnum.RECOGNITION_STATUS_TO_IDENTIFY.getCode().equals(recognitionTask.getRecognitionTaskStatus())) {
                log.error("算法识别失败，状态不对应 {}-{}", recognitionTaskGuid, picId);
            }
            List<AlgorithmResultDefectDetail> defectList = algorithmResultDetail.getDefectList();
            if (Func.isNotEmpty(defectList)) {
                int tagsNum = handleAlgorithmResultV2(recognitionTask, inspectionPicture, defectList, algorithmType);
                if (tagsNum == 0) {
                    // 有缺陷但缺陷识别位置全部异常 图片标注为正常
                    log.info("图片有缺陷但全部位置识别异常");
                    inspectionPictureService.update(
                            new LambdaUpdateWrapper<InspectionPicture>()
                                    .eq(InspectionPicture::getPicAlgorithmFlg, BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode())
                                    .eq(InspectionPicture::getGroupId, inspectionPicture.getGroupId())
                                    .eq(InspectionPicture::getRecognitionTaskId, recognitionTask.getId())
                                    .set(InspectionPicture::getPicAlgorithmFlg, BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode())
                                    .set(InspectionPicture::getPicDefectFlg, BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode())
                                    .set(InspectionPicture::getUpdateTime, new Date()).set(InspectionPicture::getUpdateUser, recognitionTask.getCreateUser()));
                }
                log.info("将图片变更为已标注");
                // 已标注图片
                inspectionPicture.setPicAlgorithmFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
                inspectionPicture.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
                inspectionPicture.setUpdateTime(new Date());
                inspectionPicture.setUpdateUser(recognitionTask.getCreateUser());
                inspectionPictureService.updateById(inspectionPicture);
            } else {
                // 正常图片
                // 红外和可见光同组状态一致
                inspectionPictureService.update(
                        new LambdaUpdateWrapper<InspectionPicture>()
                                .eq(InspectionPicture::getPicAlgorithmFlg,BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode())
                                .eq(InspectionPicture::getGroupId,inspectionPicture.getGroupId())
                                .eq(InspectionPicture::getRecognitionTaskId,recognitionTask.getId())
                                .set(InspectionPicture::getPicAlgorithmFlg,BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode())
                                .set(InspectionPicture::getPicDefectFlg,BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode())
                                .set(InspectionPicture::getUpdateTime,new Date()).set(InspectionPicture::getUpdateUser,recognitionTask.getCreateUser()));
//                inspectionPicture.setPicAlgorithmFlg(BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode());
//                inspectionPicture.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_NO_DEFECT.getCode());
            }

//            inspectionPicture.setUpdateTime(new Date());
//            inspectionPicture.setUpdateUser(recognitionTask.getCreateUser());
//            inspectionPictureService.updateById(inspectionPicture);

        } else {
            //算法识别失败
            log.info("算法识别失败失败图片 {}-{}", recognitionTaskGuid, picId);
            // 算法返回识别失败，当前图片记为识别过picDefectFlg仍为unlabeled，同步修改推送算法逻辑picDefectFlg
            inspectionPicture.setPicAlgorithmFlg(BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode());
            inspectionPictureService.updateById(inspectionPicture);
        }
        // 判断该任务下是否还有未识别的图片
        Long unlabeledLong = inspectionPictureService
                .count(new QueryWrapper<InspectionPicture>().lambda().eq(InspectionPicture::getPicAlgorithmFlg, "unlabeled")
                        .eq(InspectionPicture::getRecognitionTaskId, recognitionTaskGuid)
                        .eq(InspectionPicture::getBindFlag, StringPool.YES));
        log.info("该任务下未识别的图片数量为:{}", unlabeledLong);
        // 图片全部识别完完毕
        if (unlabeledLong < 1) {
            //InspectionTask inspectionTask = inspectionTaskService.getById(recognitionTask.getInspectionTaskId());
            InspectionTask inspectionTask = inspectionTaskMapper.selectById(recognitionTask.getInspectionTaskId());
            // 判断巡检任务是否办结 如果已办结 且 识别完成 则变成 任务完成
            if (inspectionTask.getInspectionTaskStatus().equals(THREE)) {
                recognitionTask.setRecognitionTaskStatus(BizDictEnum.RECOGNITION_STATUS_IDENTIFIED.getCode());
                // 查询出图片的所有缺陷 不筛选是否审核
                log.info("=============查询出图片的所有缺陷");
                List<InspectionPictureTagging> taggingIds;
                //过滤掉实际相同的可见光缺陷框
                //遍历 如果defect_key 在 light_defect_key中存在 则舍弃
                if (StringUtil.equals(PV, recognitionTask.getDeviceType())) {
                    taggingIds =
                            inspectionPictureTaggingMapper.getPvDefectTaggingIds(recognitionTask.getId(), StringPool.EMPTY,
                                    BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
                    // 过滤可见光缺陷
                    this.filterLightDefect(taggingIds);
                    taggingIds = this.filterRepeatDefect(taggingIds);
                } else {
                    taggingIds =
                            inspectionPictureTaggingMapper.getDefectTaggingIds(recognitionTask.getId(), StringPool.EMPTY,
                                    BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
                }
//                log.info("=============生成消缺任务");
//                RemoveTaskDTO removeTask = new RemoveTaskDTO();
//                removeTask.setDeptCode(recognitionTask.getDeptCode());
//                removeTask.setCreateUser(recognitionTask.getCreateUser());
//                removeTask.setCreateDept(recognitionTask.getCreateDept());
//                removeTask.setRemoveTaskName(recognitionTask.getInspectionTaskNo());
//                removeTask.setInspectionTaskNo(recognitionTask.getInspectionTaskNo());
//                removeTask.setDeviceType(recognitionTask.getDeviceType());
//                removeTask.setInspectionTaskId(recognitionTask.getInspectionTaskId());
//                removeTask.setInspectionPictureTaggingIds(taggingIds.stream().map(InspectionPictureTagging::getId).collect(Collectors.toList()));
//                //根据设备类型设置消缺任务初始状态：光伏设备为待下发，其他设备为执行中
//                if (BizDictEnum.DEVICE_TYPE_PV.getCode().equals(recognitionTask.getDeviceType())) {
//                    removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_TO_SEND.getCode());
//                } else {
//                    removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
//                }
//                removeTaskService.saveRemoveTask(removeTask);
            } else {
                // 如果未办结 且 识别完成 则变成 识别完成
                recognitionTask.setRecognitionTaskStatus(BizDictEnum.RECOGNITION_STATUS_IDENTIFIED.getCode());
            }

            updateById(recognitionTask);

            log.info("=============策略使用次数+1");

            // 策略使用次数+1
            strategyMapper.updateUsageTimes(recognitionTask.getStrategyId());
        }
    }

    @Override
    public void infraredAlgorithmCallback(AlgorithmResult algorithmResult, String base64Bytes, String fileName, long fileSize) {

    }
}
